<?xml version="1.0" encoding="UTF-8"?>
<component type="desktop">
 <id>io.github.mpc_qt.mpc-qt</id>
 <metadata_license>CC0-1.0</metadata_license>
 <project_license>GPL-2.0+</project_license>
 <name>MPC-QT</name>
 <summary>Media Player Classic reimplemented with Qt and libmpv</summary>
 <launchable type="desktop-id">io.github.mpc_qt.mpc-qt.desktop</launchable>
 <content_rating type="oars-1.1" />
 <developer id="io.github.mpc-qt">
  <name>The MPC-QT developers</name>
 </developer>
 <description>
  <p>
   Open source media player focusing on ease of use and effectiveness.
  </p>
  <p>Features:</p>
  <ul>
   <li>Multiple playlists</li>
   <li>Video preview on seekbar (thumbnail)</li>
   <li>Quick queueing like xmms/qmmp</li>
   <li>Play online videos (with youtube-dl)</li>
   <li>Configurable shortcuts and mouse buttons</li>
   <li>Multimedia keys support (Mpris)</li>
   <li>Web interface</li>
   <li>Thumbnails maker</li>w
  </ul>
 </description>
 <screenshots>
  <screenshot type="default">
   <caption>Main window with video preview</caption>
   <image>https://mpc-qt.github.io/images/Screenshot_main_screen_20250602_151626.png</image>
  </screenshot>
  <screenshot>
   <caption>Playlist and context menu</caption>
   <image>https://mpc-qt.github.io/images/Screenshot_context_menu_20250604_105236.png</image>
  </screenshot>
  <screenshot>
   <caption>Keys editor</caption>
   <image>https://mpc-qt.github.io/images/Screenshot_keys_editor_20250613_065949.png</image>
  </screenshot>
  <screenshot>
   <caption>Video output settings</caption>
   <image>https://mpc-qt.github.io/images/Screenshot_options_20250602_153020.png</image>
  </screenshot>
 </screenshots>
 <url type="homepage">https://mpc-qt.github.io/</url>
 <url type="bugtracker">https://github.com/mpc-qt/mpc-qt/issues</url>
 <recommends>
  <control>keyboard</control>
  <control>pointing</control>
  <control>touch</control>
 </recommends>
 <releases>
  <release version="25.07" date="2025-07-05">
    <url type="details">https://github.com/mpc-qt/mpc-qt/releases/tag/v25.07</url>
    <description>
      <p>Release description</p>
        <ul>
          <li>A video preview is shown on the seek bar.</li>
          <li>Improved shuffle feature, playlist view and new music mode.</li>
          <li>The keys editor has a search field and a better workflow.</li>
          <li>Subtitles additions for deaf or hard-of-hearing (SDH) can be automatically hidden.</li>
          <li>Bigger icons and updated subtitles icon.</li>
          <li>The interface language can be forced to English.</li>
        </ul>
    </description>
  </release>
  <release version="24.12.1" date="2025-02-25">
    <url type="details">https://github.com/mpc-qt/mpc-qt/releases/tag/v24.12.1</url>
    <description>
      <p>Release description</p>
        <ul>
          <li>Fixes a long standing bug that could lead to a crash.</li>
          <li>Fixes a regression for Nvidia GPUs introduced by previous version.</li>
          <li>Fixes the new "remember file position" feature.</li>
        </ul>
    </description>
  </release>
  <release version="24.12" date="2024-12-07">
    <url type="details">https://github.com/mpc-qt/mpc-qt/releases/tag/v24.12</url>
    <description>
      <p>Release description</p>
        <ul>
          <li>Position, audio, video and subtitle tracks can now be remembered for 1000 videos.</li>
          <li>Seek bar is easier to use.</li>
          <li>Updated hardware decoding backends.</li>
          <li>Active subtitle, audio and video tracks are shown.</li>
          <li>Subtitles delay and video aspect ratio are adjustable.</li>
          <li>Current and total time and other information shown in OSD.</li>
        </ul>
    </description>
  </release>
 </releases>
</component>
