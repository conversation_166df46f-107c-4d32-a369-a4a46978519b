name: C/C++ CI Tests

on:
  [workflow_call]

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    - name: Download mpc-qt build
      uses: actions/download-artifact@v4
      with:
        path: mpc-qt
        merge-multiple: true
    - name: Install packages
      run: |
        sudo apt-get update
        sudo apt-get -y install qt6-base-dev qt6-l10n-tools libqt6svg6-dev libgl-dev libmpv-dev xvfb x11-apps imagemagick
    - name: Start Xvfb
      uses: ./.github/actions/linux/commands/start_xvfb
    - name: Set CONFIGDIR environment variable
      run: echo "CONFIGDIR=$HOME/.config/mpc-qt" >> $GITHUB_ENV
    - name: Start without config
      uses: ./.github/actions/linux/tests/start
    - name: Start with config
      uses: ./.github/actions/linux/tests/start_with_config
    - name: Start with config and video
      uses: ./.github/actions/linux/tests/start_with_config_video
    - name: Upload screenshots
      if: ${{ !cancelled() }}
      uses: ./.github/actions/commands/upload_screenshots
