name: C/C++ CI

on:
  push:
  pull_request:

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
    - name: Install packages
      run: |
        sudo apt-get update
        sudo apt-get -y install \
        build-essential \
        cmake \
        linguist-qt6 \
        qt6-tools-dev \
        qt6-base-dev \
        libboost-all-dev \
        libqt6svg6-dev \
        libgl-dev \
        libmpv-dev
    - uses: actions/checkout@v4
      with:
        fetch-depth: 1000
        fetch-tags: true
    - name: Set version
      run: |
        echo "FORCE_VERSION=" >> $GITHUB_ENV
        if [ $( cat .github/actions/data/VERSION ) != "GithubAction"  ]; then
            echo "FORCE_VERSION=-DMPCQT_VERSION=$( cat .github/actions/data/VERSION )" >> $GITHUB_ENV
        fi
    - name: cmake
      run: cmake $FORCE_VERSION -DCMAKE_BUILD_TYPE=RelWithDebInfo -G Ninja -B build
    - name: Build
      run: cmake --build build
    - name: Save, strip and link symbols
      run: |
        cd bin
        objcopy --only-keep-debug mpc-qt mpc-qt.dbg
        objcopy --strip-debug mpc-qt
        objcopy --add-gnu-debuglink=mpc-qt.dbg mpc-qt
    - name: Upload executable
      uses: actions/upload-artifact@v4
      with:
        name: "mpc-qt-x86_64"
        path: bin/mpc-qt
    - name: Upload symbols file
      uses: actions/upload-artifact@v4
      with:
        name: "mpc-qt-x86_64-debug_symbols"
        path: bin/mpc-qt.dbg

  tests:
    name: Linux
    needs: build
    uses: ./.github/workflows/linux_tests.yml

  appimage:
    name: AppImage
    needs: tests
    uses: ./.github/workflows/appimage.yml

  flatpak:
    name: Flatpak
    needs: tests
    uses: ./.github/workflows/flatpak.yml

  pre-release:
    name: Pre-release
    if: ${{ github.ref_name == 'master' && github.repository_owner == 'mpc-qt' }}
    runs-on: ubuntu-latest
    needs: [appimage, flatpak]
    env:
      GH_TOKEN: ${{ secrets.API_TOKEN_GITHUB_DEV_BUILDS }}

    steps:
    - name: Trigger dev-builds release workflow
      run: |
        gh workflow run --repo mpc-qt/dev-builds "Release maker"
