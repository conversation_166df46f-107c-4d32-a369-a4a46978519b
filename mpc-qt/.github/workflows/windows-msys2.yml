name: <PERSON>ys2 CI

on:
  push:
  pull_request:

jobs:
  build:
    name: Build
    runs-on: windows-2022
    defaults:
      run:
        shell: msys2 {0}

    steps:
    - name: Install packages
      uses: msys2/setup-msys2@v2
      with:
        msystem: mingw64
        update: true
        install: >-
          git
          base-devel
          wget
          p7zip
          mingw-w64-x86_64-cmake
          mingw-w64-x86_64-ninja
          mingw-w64-x86_64-toolchain
          mingw-w64-x86_64-qt6
          mingw-w64-x86_64-qt-creator
          mingw-w64-x86_64-imagemagick
          mingw-w64-x86_64-boost
          mingw-w64-x86_64-mpv
          mingw-w64-x86_64-nsis
    - name: Avoid line ending issues on Windows
      run: git config --global core.autocrlf input
    - name: Download mpc-qt source
      uses: actions/checkout@v4
    - name: Download yt-dlp
      uses: robinraju/release-downloader@v1
      with:
        repository: "yt-dlp/yt-dlp"
        latest: true
        fileName: "yt-dlp.exe"
    - name: Set version
      run: echo "FORCE_VERSION=$( cat .github/actions/data/VERSION )" >> $GITHUB_ENV
    - name: Build
      run: |
        chmod +x *.sh
        set +e
        bash make-win-icon.sh
        bash make-release-msys2.sh
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: "mpc-qt-win"
        path: mpc-qt-*/
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: "mpc-qt-win-installer"
        path: deploy/dist/nsis/mpc-qt-win-x64-installer.exe
  tests:
    name: Windows
    needs: build
    uses: ./.github/workflows/windows_tests.yml
