app-id: io.github.mpc_qt.mpc-qt
runtime: org.kde.Platform
runtime-version: '6.8'
sdk: org.kde.Sdk
command: mpc-qt
rename-icon: mpc-qt
copy-icon: true
add-build-extensions:
  org.freedesktop.Platform.ffmpeg-full:
    version: '24.08'
    directory: lib/ffmpeg
    add-ld-path: .
finish-args:
  - --share=ipc
  - --share=network
# We support both Xwayland and Wayland (selectable in the Tweaks settings) because
# Wayland doesn't allow centering the window after we resize it
  - --socket=x11
  - --socket=wayland
  - --socket=pulseaudio
  - --device=dri
# These can't be :ro to access subtitles when file opened from the app
  - --filesystem=/media
  - --filesystem=/run/media
  - --filesystem=/mnt
  - --filesystem=xdg-desktop
  - --filesystem=xdg-documents
  - --filesystem=xdg-download
  - --filesystem=xdg-music
  - --filesystem=xdg-pictures
  - --filesystem=xdg-public-share
  - --filesystem=xdg-videos
  - --filesystem=xdg-run/gamescope-0:ro
  - --filesystem=xdg-run/pipewire-0:ro
  - --talk-name=org.freedesktop.ScreenSaver
  - --own-name=org.mpris.MediaPlayer2.MpcQt
  - --system-talk-name=org.freedesktop.UDisks2
# Give access to home folder to be able to load external subtitles and make downloaded mouse cursor themes from legacy ~/.icons path work
  - --filesystem=home:ro
  - --env=XCURSOR_PATH=/run/host/user-share/icons:/run/host/share/icons:~/.icons
cleanup:
  - '*.a'
  - '*.la'
  - /include
  - /lib/cmake
  - /lib/pkgconfig
  - /share/man
cleanup-commands:
  - mkdir -p /app/lib/ffmpeg
modules:
  - name: mpc-qt
    buildsystem: cmake-ninja
    config-opts:
      - -DCMAKE_PREFIX_PATH=/app/boost
    sources:
      - type: git
        url: https://github.com/mpc-qt/mpc-qt.git
        branch: master
    modules:
      - name: libmpv
        cleanup:
          - /share/bash-completion
          - /share/zsh
          - /share/doc
          - /share/icons
          - /share/applications
        buildsystem: meson
        config-opts:
          - -Dlibmpv=true
          - -Dcplayer=false
          - -Dlua=enabled
          - -Ddebug=false
          - -Dbuild-date=false
          - -Dalsa=disabled
          - -Dmanpage-build=disabled
          - -Dvulkan=enabled
        sources:
          - type: archive
            url: https://github.com/mpv-player/mpv/archive/refs/tags/v0.40.0.tar.gz
            sha256: 10a0f4654f62140a6dd4d380dcf0bbdbdcf6e697556863dc499c296182f081a3
            x-checker-data:
              type: anitya
              project-id: 5348
              stable-only: true
              url-template: https://github.com/mpv-player/mpv/archive/refs/tags/v$version.tar.gz
          - type: patch
            path: fix-hwdec.diff
        modules:
          - name: luajit
            no-autogen: true
            make-args:
              - BUILDMODE=dynamic
              - PREFIX=/app
            make-install-args:
              - PREFIX=/app
            sources:
              - type: git
                url: https://github.com/openresty/luajit2.git
                tag: v2.1-20250117
                commit: 93162f34e7424cd0ea3c4046a9ffacce621626bc
                x-checker-data:
                  type: git
                  tag-pattern: ^v([\d.-]+)$
                  stable-only: false
            cleanup:
              - /bin

          - name: vapoursynth
            config-opts:
              - --with-python-prefix=/app

            sources:
              - type: archive
                url: https://github.com/vapoursynth/vapoursynth/archive/refs/tags/R70.tar.gz
                sha256: 59c813ec36046be33812408ff00e16cae63c6843af6acf4e34595910a80e267b
                x-checker-data:
                  type: anitya
                  project-id: 15982
                  stable-only: true
                  url-template: https://github.com/vapoursynth/vapoursynth/archive/$version.tar.gz
            modules:
              - name: zimg
                config-opts:
                  - --disable-static
                cleanup:
                  - /include
                  - /lib/pkgconfig
                  - /share/doc
                sources:
                  - type: archive
                    archive-type: tar
                    url: https://api.github.com/repos/sekrit-twc/zimg/tarball/release-3.0.5
                    sha256: 1b8998f03f4a49e4d730033143722b32bc28a5306ef809ccfb3b4bbb29e4b784
                    x-checker-data:
                      type: json
                      url: https://api.github.com/repos/sekrit-twc/zimg/releases/latest
                      url-query: .tarball_url
                      version-query: .tag_name | sub("^release-"; "")
                      timestamp-query: .published_at

          - name: libplacebo
            buildsystem: meson
            config-opts:
              - -Dopengl=enabled
              - -Dvulkan=enabled
              - -Dglslang=disabled
              - -Dd3d11=disabled
              - -Ddemos=False
            sources:
              - type: archive
                url: https://code.videolan.org/videolan/libplacebo/-/archive/v7.349.0/libplacebo-v7.349.0.tar.gz
                sha256: 79120e685a1836344b51b13b6a5661622486a84e4d4a35f6c8d01679a20fbc86
                x-checker-data:
                  type: anitya
                  project-id: 20101
                  stable-only: true
                  url-template: https://code.videolan.org/videolan/libplacebo/-/archive/v$version/libplacebo-v$version.tar.gz
              - type: archive
                url: https://github.com/pallets/jinja/archive/refs/tags/3.1.6.tar.gz
                sha256: 2074b22a72caa65474902234b320d73463d6d4c223ee49f4b433495758356337
                dest: 3rdparty/jinja
                x-checker-data:
                  type: anitya
                  project-id: 3894
                  url-template: https://github.com/pallets/jinja/archive/refs/tags/$version.tar.gz
              - type: archive
                url: https://github.com/Dav1dde/glad/archive/refs/tags/v2.0.8.tar.gz
                sha256: 44f06f9195427c7017f5028d0894f57eb216b0a8f7c4eda7ce883732aeb2d0fc
                dest: 3rdparty/glad
                x-checker-data:
                  type: anitya
                  project-id: 300234
                  url-template: https://github.com/Dav1dde/glad/archive/refs/tags/v$version.tar.gz

          - name: libXpresent
            buildsystem: autotools
            sources:
              - type: archive
                url: https://xorg.freedesktop.org/archive/individual/lib/libXpresent-1.0.1.tar.xz
                sha256: b964df9e5a066daa5e08d2dc82692c57ca27d00b8cc257e8e960c9f1cf26231b
                x-checker-data:
                  type: anitya
                  project-id: 17166
                  stable-only: true
                  url-template: https://xorg.freedesktop.org/archive/individual/lib/libXpresent-$version.tar.xz

          - name: libass
            config-opts:
              - --disable-static
            sources:
              - type: archive
                url: https://github.com/libass/libass/releases/download/0.17.3/libass-0.17.3.tar.gz
                sha256: da7c348deb6fa6c24507afab2dee7545ba5dd5bbf90a137bfe9e738f7df68537
                x-checker-data:
                  type: anitya
                  project-id: 1560
                  stable-only: true
                  url-template: https://github.com/libass/libass/releases/download/$version/libass-$version.tar.gz

          - name: uchardet
            buildsystem: cmake-ninja
            config-opts:
              - -DCMAKE_BUILD_TYPE=Release
              - -DBUILD_STATIC=0
            cleanup:
              - /bin
            sources:
              - type: archive
                url: https://www.freedesktop.org/software/uchardet/releases/uchardet-0.0.8.tar.xz
                sha256: e97a60cfc00a1c147a674b097bb1422abd9fa78a2d9ce3f3fdcc2e78a34ac5f0
                x-checker-data:
                  type: anitya
                  project-id: 9265
                  stable-only: true
                  url-template: https://www.freedesktop.org/software/uchardet/releases/uchardet-$version.tar.xz

          # Needed for mpv 0.40 vaapi support
          - name: libdisplay-info
            buildsystem: meson
            sources:
              - type: archive
                url: https://gitlab.freedesktop.org/emersion/libdisplay-info/-/archive/0.2.0/libdisplay-info-0.2.0.tar.gz
                sha256: f7331fcaf5527251b84c8fb84238d06cd2f458422ce950c80e86c72927aa8c2b
                x-checker-data:
                  type: anitya
                  project-id: 326668
                  stable-only: true
                  url-template: https://gitlab.freedesktop.org/emersion/libdisplay-info/-/archive/0.2.0/libdisplay-info-$version.tar.gz

            modules:
              - name: hwdata
                sources:
                  - type: archive
                    url: https://github.com/vcrhonek/hwdata/archive/refs/tags/v0.393.tar.gz
                    sha256: 322add86944680e99d04468d49de180a7ce0c77fb406f7abde8ab14f2d161051
                    x-checker-data:
                      type: anitya
                      project-id: 5387
                      stable-only: true
                      url-template: https://github.com/vcrhonek/hwdata/archive/refs/tags/v$version.tar.gz

      - name: yt-dlp
        no-autogen: true
        no-make-install: true
        make-args:
          - yt-dlp
          - PYTHON=/usr/bin/python3
        post-install:
          - install yt-dlp /app/bin
        sources:
          - type: archive
            url: https://github.com/yt-dlp/yt-dlp/archive/refs/tags/2025.05.22.tar.gz
            sha256: c09ad17e0b3d5f23af3948b4b3dc7e1a9781e66a9a4d93b7dc631803e538b137
            x-checker-data:
              type: anitya
              project-id: 143399
              stable-only: true
              url-template: https://github.com/yt-dlp/yt-dlp/archive/refs/tags/$version.tar.gz

      - name: boost
        buildsystem: simple
        build-commands:
          - ./bootstrap.sh
          - ./b2 install --prefix=/app/boost
        sources:
          - type: archive
            url: https://github.com/boostorg/boost/releases/download/boost-1.88.0/boost-1.88.0-cmake.tar.gz
            sha256: dcea50f40ba1ecfc448fdf886c0165cf3e525fef2c9e3e080b9804e8117b9694
            x-checker-data:
              type: anitya
              project-id: 6845
              stable-only: true
              url-template: https://github.com/boostorg/boost/releases/download/boost-$version/boost-$version-cmake.tar.gz
