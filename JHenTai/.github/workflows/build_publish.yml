name: Build & Release

# Trigger on push to master branch or with a tag
on:
  push:
    branches:
      - '**'
    tags:
      - v*

# If previous workflow is still running, we push again, we will cancel the previous workflow
concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  Build:
    strategy:
      fail-fast: false
      matrix:
        include:
          - target: Android
            os: ubuntu-latest
            artifact_name: release-Android
            artifact_path: build/app/outputs/apk/release/*.apk
          - target: Windows
            os: windows-latest
            artifact_name: release-Windows
            artifact_path: build/windows/*.zip
          - target: Linux-x64-deb
            os: ubuntu-22.04
            artifact_name: release-Linux-x64-deb
            artifact_path: |
              build/linux/*.deb
#          - target: Linux-x64-AppImage
#            os: ubuntu-22.04
#            artifact_name: release-Linux-x64-AppImage
#            artifact_path: |
#              build/linux/*.AppImage
          - target: Linux-arm64-deb
            os: ubuntu-22.04-arm
            artifact_name: release-Linux-arm64-deb
            artifact_path: |
              build/linux/*.deb
          - target: iOS
            os: macos-latest
            cache_pod_key: ios-pods
            cache_pod_path: ios/Pods
            cache_pod_restore_keys_hash_file: ios/Podfile.lock
            artifact_name: release-iOS
            artifact_path: build/**/*.ipa
          - target: macOS
            os: macos-latest
            cache_pod_key: macos-pods
            cache_pod_path: macos/Pods
            cache_pod_restore_keys_hash_file: macos/Podfile.lock
            artifact_name: release-macOS
            artifact_path: /Users/<USER>/work/JHenTai/JHenTai/*.dmg
    outputs:
      version: ${{ steps.get_version.outputs.version }}
      arch: ${{ steps.get_version.outputs.arch}}
    runs-on: ${{ matrix.os }}
    env:
      FLUTTER_VERSION: 3.24.4
    steps:
      # Checkout branch
      - name: Checkout
        uses: actions/checkout@v3
      # Add Android keystore
      - name: Setup Android keystore
        if: matrix.target == 'Android'
        run: |
          echo "${{ secrets.ENCODED_KEYSTORE }}" | base64 -di > android/app/upload-keystore.jks
          echo "${{ secrets.KEY_PROPERTIES }}" > android/key.properties
      # Add JHenTai api serret
      - name: Setup JHenTai API Secret
        run: |
          echo "${{ secrets.JH_API_SECRET_CONFIG }}" > lib/src/config/jh_api_secret_config.dart
      # Setup Flutter
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'master'
          cache: true
      # Output Flutter Version
      - name: Output Flutter Version
        run: flutter --version
      # Cache Pod
      - name: Cache Pod
        if: matrix.cache_pod_key != null
        uses: actions/cache@v3
        with:
          key: ${{ matrix.cache_pod_key }}
          path: ${{ matrix.cache_pod_path }}
          restore-keys: ${{ matrix.cache_key }}-${{ hashFiles(matrix.cache_pod_restore_keys_hash_file)}}
      # Setup JDK
      - name: Setup JDK 17 (Android)
        if: matrix.target == 'Android'
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'
          cache: gradle
      # Xcodebuild
      - name: Build Xcode
        if: matrix.os == 'macos-latest'
        run: xcodebuild -resolvePackageDependencies -workspace ios/Runner.xcworkspace -scheme Runner -configuration Release
      # Flutter Pub Get
      - name: Flutter Pub Get
        run: |
          git config --global core.longpaths true
          flutter pub get
      # Get app version
      - name: Get app version
        id: get_version
        shell: bash
        run: |
          echo "version=$(head -n 5 pubspec.yaml | tail -n 1 | cut -d ' ' -f 2)" >> $GITHUB_OUTPUT
          echo "arch=$(echo $RUNNER_ARCH | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT
      # Build Android .apk
      - name: Build Android
        if: matrix.target == 'Android'
        run: |
          flutter build apk -t lib/src/main.dart --release --split-per-abi
          cd build/app/outputs/apk/release
          mv app-arm64-v8a-release.apk JHenTai-${{ steps.get_version.outputs.version }}-arm64-v8a.apk
          mv app-armeabi-v7a-release.apk JHenTai-${{ steps.get_version.outputs.version }}-armeabi-v7a.apk
          mv app-x86_64-release.apk JHenTai-${{ steps.get_version.outputs.version }}-x64.apk
      # Build iOS .ipa
      - name: Build iOS
        if: matrix.target == 'ios'
        run: |
          flutter precache --ios
          cd ios
          pod update
          cd ..
          flutter build ios -t lib/src/main.dart --release --no-codesign
          sh thin-payload.sh build/ios/iphoneos/*.app
          cd build
          mkdir -p Payload
          mv ios/iphoneos/*.app Payload
          zip -9 JHenTai_${{ steps.get_version.outputs.version }}.ipa -r Payload
      # Build macOS .dmg
      - name: Build macOS
        if: matrix.target == 'macOS'
        run: |
          flutter precache --macos
          cd macos
          pod update
          cd ..
          sh dmg.sh
      # Build Windows .zip
      - name: Build Windows
        if: matrix.target == 'Windows'
        run: |
          flutter build windows -t lib/src/main.dart --release
          $DestDir = "build\windows\JHenTai_${{ steps.get_version.outputs.version }}"
          $SrcDir = "build\windows\x64\runner\Release"
          New-Item -Path $DestDir -ItemType Directory
          Copy-Item $SrcDir\* -Recurse $DestDir
          Copy-Item -Filter *.dll -Path windows\* -Destination $DestDir -Force
          Compress-Archive $DestDir build\windows\JHenTai_${{ steps.get_version.outputs.version }}_Windows.zip
      # Build Linux .deb
      - name: Build Linux deb
        if: matrix.target == 'Linux-x64-deb' || matrix.target == 'Linux-arm64-deb'
        run: |
          # Prepare build depends
          sudo apt-get update -y
          sudo apt-get install -y ninja-build libfuse2 webkit2gtk-4.1
          
          # Compile
          flutter build linux --release -t lib/src/main.dart

          # Build debian package
          mkdir -p build/linux/JHenTai-${{ steps.get_version.outputs.version }}-Linux-${{ steps.get_version.outputs.arch }}
          cd build/linux/JHenTai-${{ steps.get_version.outputs.version }}-Linux-${{ steps.get_version.outputs.arch }}
          mkdir -p opt/jhentai
          mkdir -p usr/share/applications
          mkdir -p usr/share/icons/hicolor/512x512/apps
          cp -r ../${{ steps.get_version.outputs.arch }}/release/bundle/* opt/jhentai
          cp -r ../../../linux/assets/DEBIAN .
          chmod 0755 DEBIAN/postinst
          chmod 0755 DEBIAN/postrm

          cp ../../../linux/assets/top.jtmonster.jhentai.desktop usr/share/applications
          cp ../../../assets/icon/JHenTai_512.png usr/share/icons/hicolor/512x512/apps/top.jtmonster.jhentai.png

          sed -i "/^Version: /s/Version: .*/Version: ${{ steps.get_version.outputs.version }}/" DEBIAN/control
          if [ "${{ steps.get_version.outputs.arch }}" = "arm64" ]; then
              sed -i "/^Architecture: /s/Architecture: .*/Architecture: arm64/" DEBIAN/control
          fi

          cd ..
          dpkg-deb --build --root-owner-group JHenTai-${{ steps.get_version.outputs.version }}-Linux-${{ steps.get_version.outputs.arch }}
      # Build Linux .AppImage
      - name: Build Linux AppImage
        if: matrix.target == 'Linux-x64-AppImage'
        run: |
          # Prepare build depends
          sudo apt-get update -y
          sudo apt-get install -y ninja-build libfuse2 webkit2gtk-4.1
          
          # Compile
          flutter build linux --release -t lib/src/main.dart
          
          cd build/linux
          
          mkdir AppDir
          cp -r x64/release/bundle/* AppDir
          mkdir -p AppDir/usr/share/icons/hicolor/512x512/apps/
          mkdir -p AppDir/usr/share/applications
          cp ../../linux/assets/top.jtmonster.jhentai.desktop AppDir/usr/share/applications
          cp ../../assets/icon/JHenTai_512.png AppDir/usr/share/icons/hicolor/512x512/apps/top.jtmonster.jhentai.png
          
          wget -O appimage-builder https://github.com/AppImageCrafters/appimage-builder/releases/download/v1.1.0/appimage-builder-1.1.0-x86_64.AppImage
          chmod +x appimage-builder
          ./appimage-builder --skip-tests --recipe ../../linux/assets/AppImageBuilder.yml
          
          mv JHenTai-latest-x86_64.AppImage JHenTai-${{ steps.get_version.outputs.version }}-Linux-x64.AppImage
      # Upload Artifacts
      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact_name }}
          path: ${{ matrix.artifact_path }}
          if-no-files-found: error
  Publish:
    if: startsWith(github.ref, 'refs/tags/')
    name: Publish
    needs: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Make tmp dir
        run: mkdir /tmp/artifacts
      - name: Download all Artifacts
        uses: actions/download-artifact@v4
        with:
          path: /tmp/artifacts
      - name: List all Artifacts
        run: ls -R /tmp/artifacts
      - name: Upload to release
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ github.ref_name }}
          allowUpdates: true
          bodyFile: changelog/${{ github.ref_name }}.md
          artifacts: /tmp/artifacts/release-Android/*.apk,/tmp/artifacts/release-iOS/*.ipa,/tmp/artifacts/release-macOS/*.dmg,/tmp/artifacts/release-Windows/*.zip,/tmp/artifacts/release-Linux-x64-deb/*.deb,/tmp/artifacts/release-Linux-arm64-deb/*.deb
          artifactErrorsFailBuild: true
          replacesArtifacts: true
