name: j<PERSON><PERSON>
description: A flutter app for E-Hentai/EXHentai

publish_to: 'none'
version: 8.0.9+296

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  # rewrite file mode
  dio:
    git:
      url: https://github.com/jiangtian616/dio
      path: dio
      ref: append-mode
  get: 4.6.6
  get_storage: 2.0.3
  extended_nested_scroll_view: 6.1.1
  html: 0.15.1
  logger: 2.2.0
  flutter_rating_bar: 4.0.1
  waterfall_flow: 3.0.2
  extended_image: 8.1.0
  font_awesome_flutter: 9.2.0
  intl: 0.19.0
  collection: 1.19.0
  clipboard: 0.1.3
  scrollable_positioned_list: 
    git:
      url: https://github.com/jiangtian616/flutter.widgets
      path: packages/scrollable_positioned_list
  flutter_list_view: 1.1.21
  executor: 2.2.2
  retry: 3.1.2
  path: 1.9.0
  drift: 2.21.0
  sqlite3_flutter_libs: 0.5.32
  path_provider: 2.1.3
  # https://github.com/letsar/flutter_slidable/issues/488
  flutter_slidable:
    git:
      url: https://github.com/letsar/flutter_slidable.git
      ref: master
  flutter_widget_from_html: 0.15.2
  url_launcher: 6.3.1
  # add onLongPress
  like_button:
    git: https://github.com/jiangtian616/like_button
  webview_flutter: 4.2.2
  desktop_webview_window:
    git:
      url: https://github.com/MixinNetwork/flutter-plugins
      path: packages/desktop_webview_window
      ref: d0c91d9c3ea12883d38215adc48586753fb41dde
  # rewrite double tap & tap drag gesture
  photo_view:
    git:
      url: https://github.com/jiangtian616/photo_view
      ref: separate
  zoom_view: 
    git: https://github.com/jiangtian616/zoom_view
  package_info_plus: 8.0.3
  file_picker: 8.1.3
  local_auth: 2.2.0
  blur: 3.1.0
  flutter_windowmanager_plus: 1.0.1
  animate_do: 3.3.4
  share_plus: 10.1.1
  syncfusion_flutter_charts: 27.1.48
  archive: 3.6.1
  # https://github.com/ponnamkarthik/FlutterToast/pull/542
  fluttertoast: 
    git: 
      url: https://github.com/MarlonJD/FlutterToast
      ref: patch-1
  battery_plus: 6.2.0
  throttling: 1.0.0
  receive_sharing_intent: 
    git: https://github.com/KasemJaffer/receive_sharing_intent
  window_manager: 0.3.9
  permission_handler: 11.3.1
  grouped_list: 5.1.2
  flutter_resizable_container: 2.0.0
  loading_animation_widget: 1.2.0+4
  simple_animations: 5.0.2
  animated_flip_counter: 0.2.6
  saver_gallery: 3.0.5
  system_network_proxy: 
    git: 
      url: https://github.com/jiangtian616/system_network_proxy
      path: system_network_proxy
  http_proxy: 
    git: https://github.com/jiangtian616/http_proxy
  flutter_socks_proxy:
    # https://github.com/tayoji-io/socks_proxy/issues/7
    git: https://github.com/jiangtian616/socks_proxy
  flutter_draggable_gridview:
    # remove assert
    git:
      url: https://github.com/jiangtian616/flutter_draggable_gridview
  flutter_displaymode: 0.6.0
  pinput: 2.2.31
  wakelock_plus: 1.2.8
  flex_color_picker: 3.1.0
  zoom_widget: 2.0.1
  android_intent_plus: 5.3.0
  integral_isolates: 0.4.1
  screen_brightness: 0.2.2+1
  crypto: 3.0.3
  csv: 5.1.1
  dotted_border: 2.1.0
  j_downloader:
    git: https://github.com/jiangtian616/JDownloader
  device_info_plus: 11.1.0
  template_expressions: 3.2.0+7
  uuid: 4.4.0
  xml: 6.5.0
  telegram: 0.0.9
  
dependency_overrides:
  test_api: 0.6.0
  matcher: 0.12.16
  chewie: 1.7.0
  http: 1.1.0
  cached_network_image: 3.3.0
  dio:
    git:
      url: https://github.com/jiangtian616/dio
      path: dio
      ref: append-mode
  collection: 1.19.0
    
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0
  test: 1.25.2
  drift_dev: 2.21.1
  build_runner: 2.4.11
  frontend_server_client: 4.0.0
  flutter_launcher_icons: 0.14.1

flutter:
  uses-material-design: true