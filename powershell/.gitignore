# PowerShell Project .gitignore
# Generated for PowerShell scripts, credentials, logs, and reports

# =============================================================================
# SECURITY & CREDENTIALS - NEVER COMMIT THESE
# =============================================================================

# Credential files (XML and other formats)
**/Credentials/
**/Creds/
**/*Creds*.xml
**/*Credentials*.xml
**/*Password*.xml
**/*Secret*.xml
**/*Key*.xml
**/*Token*.xml
**/windowsCreds.xml
**/prdDomainCreds.xml
**/ppeDomainCreds.xml
**/devDomainCreds.xml
**/cliCreds.xml

# API Keys and secrets
**/apikeys/
**/*apikey*
**/*secret*
**/*token*
.env
.env.local
.env.*.local

# Certificate files
**/*.pfx
**/*.p12
**/*.pem
**/*.key
**/*.crt
**/*.cer
**/certificates/
**/certs/

# Connection strings and database credentials
**/connectionstrings.json
**/dbconfig.json
**/database.config

# =============================================================================
# LOGS & TEMPORARY FILES
# =============================================================================

# Log files
**/*.log
**/Logs/
**/logs/
**/*_Install.log
**/*_Error.log
**/*_Debug.log
**/*_Trace.log
**/SystemReport_*.log
**/HealthCheck_*.log
**/NetworkTest_*.log
**/Deployment_*.log
**/SystemCleanup_*.log

# Temporary files
**/Temp/
**/temp/
**/tmp/
**/*.tmp
**/*.temp
**/~$*

# PowerShell transcript files
**/PowerShell_transcript.*
**/transcript*.txt

# =============================================================================
# REPORTS & OUTPUT FILES
# =============================================================================

# Generated reports
**/Reports/
**/reports/
**/output/
**/Output/
**/*Report_*.csv
**/*Report_*.xlsx
**/*Report_*.html
**/*Report_*.json
**/SystemReport_*
**/HealthCheck_*
**/NetworkTest_*
**/Deployment_*
**/SystemCleanup_*

# Backup files
**/Backups/
**/backups/
**/backup/
**/*.bak
**/*.backup

# Archive files
**/Archives/
**/archives/
**/LogArchives/
**/*.zip
**/*.7z
**/*.rar
**/*.tar.gz

# =============================================================================
# DEVELOPMENT & IDE FILES
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# PowerShell ISE
**/*.psess

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Notepad++
*.npp

# =============================================================================
# SYSTEM & OS FILES
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# PACKAGE MANAGERS & DEPENDENCIES
# =============================================================================

# PowerShell modules (if locally installed)
**/Modules/
**/modules/
# But keep custom modules in source control
!**/Modules/Custom/
!**/modules/custom/

# NuGet packages
**/packages/
*.nupkg
*.snupkg

# Chocolatey
**/tools/chocolateyInstall.ps1
**/tools/chocolateyUninstall.ps1

# =============================================================================
# APPLICATION-SPECIFIC FILES
# =============================================================================

# SCOM/SCCM/Qualys specific
**/ServerInstalls/
**/AutoDeploy/
**/QualysAgents/
**/SCOMAgent/
**/SCCMClient/

# Installation packages
**/*.msi
**/*.exe
**/*.cab
# Exception: Keep small utility executables
!**/tools/*.exe
!**/utilities/*.exe

# Configuration overrides (keep templates, ignore instances)
**/config.local.json
**/appsettings.local.json
**/web.local.config
# Keep template files
!**/*.template.*
!**/*.example.*

# =============================================================================
# TESTING & DEBUGGING
# =============================================================================

# Test results
**/TestResults/
**/test-results/
**/*.trx
**/*.coverage
**/*.coveragexml

# Debug files
**/*.pdb
**/*.ilk
**/*.meta
**/*.obj
**/*.iobj
**/*.pch
**/*.ipch
**/*.pgc
**/*.pgd
**/*.rsp
**/*.sbr
**/*.tlb
**/*.tli
**/*.tlh
**/*.tmp_proj
**/*.vspscc
**/*.vssscc
.builds
**/*.pidb
**/*.svclog
**/*.scc

# =============================================================================
# DOCUMENTATION & HELP FILES
# =============================================================================

# Generated documentation
**/docs/generated/
**/help/generated/
**/*.chm
**/*.chi

# =============================================================================
# CUSTOM EXCLUSIONS
# =============================================================================

# Server lists (may contain sensitive server names)
**/serverlist.txt
**/servers.txt
**/production-servers.txt
**/staging-servers.txt

# Custom configuration files with sensitive data
**/custom-config.json
**/local-settings.json
**/environment-specific.json

# Deployment packages
**/deployment-packages/
**/releases/

# Performance monitoring data
**/performance-data/
**/*.perfmon
**/*.blg

# =============================================================================
# EXCEPTIONS - KEEP THESE FILES
# =============================================================================

# Keep important configuration templates
!**/scriptConfigs.json
!**/config.template.json
!**/settings.template.json

# Keep documentation
!README.md
!CHANGELOG.md
!LICENSE
!CONTRIBUTING.md
!docs/**/*.md

# Keep template files
!**/Templates/
!**/*.template.ps1
!**/*.example.ps1

# Keep sample/demo files
!**/samples/
!**/examples/
!**/demo/
