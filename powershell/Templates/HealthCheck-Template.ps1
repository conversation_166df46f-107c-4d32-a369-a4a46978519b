<#
.SYNOPSIS
    Performs comprehensive health checks on Windows systems with customizable metrics and reporting.

.DESCRIPTION
    This script performs detailed health checks on Windows systems including:
    1. System resource monitoring (CPU, Memory, Disk space)
    2. Critical service status validation
    3. Event log analysis for errors and warnings
    4. Performance counter collection
    5. Network connectivity testing
    6. Application-specific health checks
    
    The script supports both local and remote execution with multiple output formats
    including JSON, HTML reports, and email notifications. All configuration values
    are loaded from the centralized scriptConfigs.json file.

.PARAMETER ServerName
    Optional remote server name. If provided, the script will perform health checks on the remote server.

.PARAMETER CheckType
    Type of health check to perform. Valid values: "Basic", "Detailed", "Critical", "All". Default is "Basic".

.PARAMETER Services
    Comma-separated list of critical services to check. If not specified, uses default list from configuration.

.PARAMETER OutputFormat
    Output format for results. Valid values: "Console", "JSO<PERSON>", "HTML", "Email". Default is "Console".

.PARAMETER OutputPath
    Path where reports will be saved. Default value is loaded from scriptConfigs.json.

.PARAMETER EmailRecipients
    Comma-separated list of email recipients for health check reports.

.PARAMETER ThresholdCPU
    CPU usage threshold percentage. Default is 80%.

.PARAMETER ThresholdMemory
    Memory usage threshold percentage. Default is 85%.

.PARAMETER ThresholdDisk
    Disk usage threshold percentage. Default is 90%.

.PARAMETER IncludePerformanceCounters
    Include detailed performance counter collection.

.PARAMETER IncludeEventLogs
    Include event log analysis for errors and warnings.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for remote connections.

.PARAMETER CredentialTarget
    Specifies which credential file to use. Valid values: "ppe", "dev", "admin", "cli".

.EXAMPLE
    # Basic health check on local system
    .\HealthCheck-Template.ps1

.EXAMPLE
    # Detailed health check with HTML report
    .\HealthCheck-Template.ps1 -CheckType "Detailed" -OutputFormat "HTML" -OutputPath "C:\Reports"

.EXAMPLE
    # Remote health check with email notification
    .\HealthCheck-Template.ps1 -ServerName "Server01" -OutputFormat "Email" -EmailRecipients "<EMAIL>"

.EXAMPLE
    # Critical services check with custom thresholds
    .\HealthCheck-Template.ps1 -CheckType "Critical" -ThresholdCPU 70 -ThresholdMemory 80 -Services "Spooler,BITS,Themes"

.NOTES
    File Name   : HealthCheck-Template.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in centralized location
    Compatible  : Windows Server 2016, 2019, 2022
    
    Version 1.0 - Initial template for system health checks
    Version 1.1 - Added performance counters and event log analysis
    Version 1.2 - Added email notifications and HTML reporting
#>

[CmdletBinding()]
param(
    [string]$ServerName,
    
    [ValidateSet("Basic", "Detailed", "Critical", "All")]
    [string]$CheckType = "Basic",
    
    [string]$Services,
    
    [ValidateSet("Console", "JSON", "HTML", "Email")]
    [string]$OutputFormat = "Console",
    
    [string]$OutputPath,
    
    [string]$EmailRecipients,
    
    [ValidateRange(1, 100)]
    [int]$ThresholdCPU = 80,
    
    [ValidateRange(1, 100)]
    [int]$ThresholdMemory = 85,
    
    [ValidateRange(1, 100)]
    [int]$ThresholdDisk = 90,
    
    [switch]$IncludePerformanceCounters,
    
    [switch]$IncludeEventLogs,
    
    [switch]$UseStoredCredentials,
    
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
    [ValidateSet("ppe", "dev", "admin", "cli", "")]
    [string]$CredentialTarget = ""
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Resolve-Path "$currentPath\..\..\..\Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Paths ###
$credPath = $config.GlobalConfig.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adminCredLocation = "$credPath\windowsCreds.xml"
$mudCreds = "$credPath\prdDomainCreds.xml"
$ppeCreds = "$credPath\ppeDomainCreds.xml"
$devCreds = "$credPath\devDomainCreds.xml"

### Script Variables ###
$ErrorActionPreference = "Continue"  # Continue on errors for health checks
$dateStamp = Get-Date -Format "yyyy-MM-dd"
$timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

### Health Check Configuration - Loaded from scriptConfigs.json ###
# TODO: Add HealthCheckConfig section to your scriptConfigs.json
$defaultServices = @("Spooler", "BITS", "Themes", "Winmgmt", "EventLog")
$defaultOutputPath = if ($config.GlobalConfig.ReportPath) { $config.GlobalConfig.ReportPath } else { "C:\Reports" }
$logPath = "$defaultOutputPath\HealthCheck_$dateStamp.log"

### Set default values from config if not provided ###
if (-not $OutputPath) {
    $OutputPath = $defaultOutputPath
}

if (-not $Services) {
    $Services = $defaultServices -join ","
}

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
    
    $objectReturn = @{
        computerName = $env:COMPUTERNAME
        checkType    = $CheckType
        timestamp    = $timeStamp
        thresholds   = @{
            cpu    = $ThresholdCPU
            memory = $ThresholdMemory
            disk   = $ThresholdDisk
        }
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $logPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-HealthCheckCredentials {
    Write-Log "Getting credentials for health check..."
    
    if ($UseStoredCredentials) {
        try {
            $credFile = $mudCreds
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials for user: $($storedCred.UserName)"
                return $storedCred
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    return $null
}

function Test-SystemResources {
    Write-Log "Checking system resources..."
    
    $results = @{
        CPU = @{}
        Memory = @{}
        Disk = @{}
        Status = "Healthy"
        Issues = @()
    }
    
    try {
        # CPU Usage
        $cpuUsage = (Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average).Average
        $results.CPU = @{
            Usage = $cpuUsage
            Threshold = $ThresholdCPU
            Status = if ($cpuUsage -gt $ThresholdCPU) { "Warning" } else { "OK" }
        }
        
        if ($cpuUsage -gt $ThresholdCPU) {
            $results.Issues += "CPU usage is $cpuUsage% (threshold: $ThresholdCPU%)"
            $results.Status = "Warning"
        }
        
        # Memory Usage
        $memory = Get-WmiObject -Class Win32_OperatingSystem
        $memoryUsage = [math]::Round((($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / $memory.TotalVisibleMemorySize) * 100, 2)
        $results.Memory = @{
            Usage = $memoryUsage
            TotalGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
            FreeGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
            Threshold = $ThresholdMemory
            Status = if ($memoryUsage -gt $ThresholdMemory) { "Warning" } else { "OK" }
        }
        
        if ($memoryUsage -gt $ThresholdMemory) {
            $results.Issues += "Memory usage is $memoryUsage% (threshold: $ThresholdMemory%)"
            $results.Status = "Warning"
        }
        
        # Disk Usage
        $disks = Get-WmiObject -Class Win32_LogicalDisk -Filter "DriveType=3"
        $results.Disk = @{}
        
        foreach ($disk in $disks) {
            $diskUsage = [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 2)
            $results.Disk[$disk.DeviceID] = @{
                Usage = $diskUsage
                TotalGB = [math]::Round($disk.Size / 1GB, 2)
                FreeGB = [math]::Round($disk.FreeSpace / 1GB, 2)
                Threshold = $ThresholdDisk
                Status = if ($diskUsage -gt $ThresholdDisk) { "Warning" } else { "OK" }
            }
            
            if ($diskUsage -gt $ThresholdDisk) {
                $results.Issues += "Disk $($disk.DeviceID) usage is $diskUsage% (threshold: $ThresholdDisk%)"
                $results.Status = "Warning"
            }
        }
        
        Write-Log "System resources check completed"
        return $results
        
    } catch {
        Write-Log "Error checking system resources: $($_.Exception.Message)" -Level "ERROR"
        $results.Status = "Error"
        $results.Issues += "Failed to check system resources: $($_.Exception.Message)"
        return $results
    }
}

function Test-CriticalServices {
    Write-Log "Checking critical services..."
    
    $serviceList = $Services -split ","
    $results = @{
        Services = @{}
        Status = "Healthy"
        Issues = @()
    }
    
    try {
        foreach ($serviceName in $serviceList) {
            $service = Get-Service -Name $serviceName.Trim() -ErrorAction SilentlyContinue
            
            if ($service) {
                $results.Services[$serviceName] = @{
                    Status = $service.Status.ToString()
                    StartType = $service.StartType.ToString()
                    DisplayName = $service.DisplayName
                }
                
                if ($service.Status -ne "Running" -and $service.StartType -eq "Automatic") {
                    $results.Issues += "Service '$serviceName' is not running (Status: $($service.Status))"
                    $results.Status = "Warning"
                }
            } else {
                $results.Services[$serviceName] = @{
                    Status = "NotFound"
                    StartType = "Unknown"
                    DisplayName = "Service not found"
                }
                $results.Issues += "Service '$serviceName' not found on system"
                $results.Status = "Warning"
            }
        }
        
        Write-Log "Critical services check completed"
        return $results
        
    } catch {
        Write-Log "Error checking services: $($_.Exception.Message)" -Level "ERROR"
        $results.Status = "Error"
        $results.Issues += "Failed to check services: $($_.Exception.Message)"
        return $results
    }
}

function Test-EventLogs {
    if (-not $IncludeEventLogs -and $CheckType -ne "Detailed" -and $CheckType -ne "All") {
        return @{ Status = "Skipped"; Message = "Event log analysis not requested" }
    }
    
    Write-Log "Analyzing event logs..."
    
    $results = @{
        Errors = @()
        Warnings = @()
        Status = "Healthy"
        Summary = @{}
    }
    
    try {
        $yesterday = (Get-Date).AddDays(-1)
        
        # Check System log for errors
        $systemErrors = Get-WinEvent -FilterHashtable @{LogName='System'; Level=2; StartTime=$yesterday} -MaxEvents 50 -ErrorAction SilentlyContinue
        $systemWarnings = Get-WinEvent -FilterHashtable @{LogName='System'; Level=3; StartTime=$yesterday} -MaxEvents 50 -ErrorAction SilentlyContinue
        
        # Check Application log for errors
        $appErrors = Get-WinEvent -FilterHashtable @{LogName='Application'; Level=2; StartTime=$yesterday} -MaxEvents 50 -ErrorAction SilentlyContinue
        $appWarnings = Get-WinEvent -FilterHashtable @{LogName='Application'; Level=3; StartTime=$yesterday} -MaxEvents 50 -ErrorAction SilentlyContinue
        
        $results.Summary = @{
            SystemErrors = if ($systemErrors) { $systemErrors.Count } else { 0 }
            SystemWarnings = if ($systemWarnings) { $systemWarnings.Count } else { 0 }
            ApplicationErrors = if ($appErrors) { $appErrors.Count } else { 0 }
            ApplicationWarnings = if ($appWarnings) { $appWarnings.Count } else { 0 }
        }
        
        if ($results.Summary.SystemErrors -gt 10 -or $results.Summary.ApplicationErrors -gt 10) {
            $results.Status = "Warning"
        }
        
        Write-Log "Event log analysis completed"
        return $results
        
    } catch {
        Write-Log "Error analyzing event logs: $($_.Exception.Message)" -Level "ERROR"
        $results.Status = "Error"
        return $results
    }
}

# TODO: Add additional health check functions:
# - Test-NetworkConnectivity
# - Get-PerformanceCounters  
# - Test-ApplicationHealth
# - Generate-HTMLReport
# - Send-EmailReport

### Main Execution ###
try {
    Write-Log "=== Health Check Started ==="
    Write-Log "Check Type: $CheckType"
    Write-Log "Target: $(if ($ServerName) { $ServerName } else { 'Local System' })"
    
    $healthResults = @{
        SystemInfo = @{
            ComputerName = if ($ServerName) { $ServerName } else { $env:COMPUTERNAME }
            CheckType = $CheckType
            Timestamp = $timeStamp
        }
        OverallStatus = "Healthy"
        Issues = @()
    }
    
    # Perform health checks based on type
    if ($CheckType -in @("Basic", "All")) {
        $healthResults.Resources = Test-SystemResources
        $healthResults.Services = Test-CriticalServices
        
        if ($healthResults.Resources.Status -ne "Healthy") { $healthResults.OverallStatus = $healthResults.Resources.Status }
        if ($healthResults.Services.Status -ne "Healthy") { $healthResults.OverallStatus = $healthResults.Services.Status }
        
        $healthResults.Issues += $healthResults.Resources.Issues
        $healthResults.Issues += $healthResults.Services.Issues
    }
    
    if ($CheckType -in @("Detailed", "All")) {
        $healthResults.EventLogs = Test-EventLogs
        
        if ($healthResults.EventLogs.Status -ne "Healthy") { $healthResults.OverallStatus = $healthResults.EventLogs.Status }
    }
    
    # Output results based on format
    switch ($OutputFormat) {
        "JSON" {
            $jsonResult = New-JsonReturn -Success "true" -Status $healthResults.OverallStatus -Message "Health check completed" -Data $healthResults
            Write-Output $jsonResult
            
            if ($OutputPath) {
                $jsonFile = "$OutputPath\HealthCheck_$($env:COMPUTERNAME)_$dateStamp.json"
                $jsonResult | Out-File -FilePath $jsonFile -Encoding UTF8
                Write-Log "JSON report saved to: $jsonFile"
            }
        }
        "Console" {
            Write-Host "`n=== HEALTH CHECK RESULTS ===" -ForegroundColor Cyan
            Write-Host "Overall Status: $($healthResults.OverallStatus)" -ForegroundColor $(if ($healthResults.OverallStatus -eq "Healthy") { "Green" } else { "Yellow" })
            
            if ($healthResults.Issues.Count -gt 0) {
                Write-Host "`nIssues Found:" -ForegroundColor Red
                foreach ($issue in $healthResults.Issues) {
                    Write-Host "  - $issue" -ForegroundColor Yellow
                }
            } else {
                Write-Host "`nNo issues found!" -ForegroundColor Green
            }
        }
        default {
            Write-Log "Output format '$OutputFormat' not yet implemented" -Level "WARNING"
        }
    }
    
    Write-Log "=== Health Check Completed ==="
    
} catch {
    Write-Log "=== Health Check Failed ===" -Level "ERROR"
    Write-Log "Error: $($_.Exception.Message)" -Level "ERROR"
    
    if ($OutputFormat -eq "JSON") {
        $errorResponse = New-JsonReturn -Success "false" -Status "failed" -Message "Health check failed: $($_.Exception.Message)"
        Write-Output $errorResponse
    }
    
    exit 1
}
