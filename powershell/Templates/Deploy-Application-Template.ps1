<#
.SYNOPSIS
    Deploys applications to Windows systems with multi-stage deployment and rollback capabilities.

.DESCRIPTION
    This script provides a comprehensive application deployment framework including:
    1. Pre-deployment validation and prerequisites checking
    2. Multi-stage deployment process (staging, production)
    3. Configuration file management and transformation
    4. Service coordination and dependency management
    5. Post-deployment validation and testing
    6. Automatic rollback capabilities on failure
    7. Deployment verification and health checks
    
    The script supports both local and remote deployments with detailed logging,
    progress reporting, and notification capabilities. All configuration values
    are loaded from the centralized scriptConfigs.json file.

.PARAMETER ApplicationName
    Name of the application to deploy. Must match a configured application in scriptConfigs.json.

.PARAMETER Version
    Version of the application to deploy. If not specified, uses latest available version.

.PARAMETER Environment
    Target environment for deployment. Valid values: "Development", "Staging", "Production". Default is "Development".

.PARAMETER ServerList
    Comma-separated list of servers for deployment. If not specified, uses servers from configuration.

.PARAMETER ServerListFile
    Path to a text file containing server names (one per line).

.PARAMETER DeploymentType
    Type of deployment. Valid values: "Fresh", "Upgrade", "Rollback", "Hotfix". Default is "Fresh".

.PARAMETER SourcePath
    Path to the application deployment package. If not specified, uses path from configuration.

.PARAMETER BackupLocation
    Location for backup files during deployment. Default value is loaded from scriptConfigs.json.

.PARAMETER ConfigTransform
    Apply configuration transformations for the target environment.

.PARAMETER SkipPreValidation
    Skip pre-deployment validation checks.

.PARAMETER SkipPostValidation
    Skip post-deployment validation checks.

.PARAMETER Force
    Force deployment even if validation checks fail.

.PARAMETER DryRun
    Perform a dry run without making actual changes.

.PARAMETER OutputFormat
    Output format for deployment results. Valid values: "Console", "JSON", "HTML". Default is "Console".

.PARAMETER EmailRecipients
    Comma-separated list of email recipients for deployment notifications.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for remote deployments.

.PARAMETER CredentialTarget
    Specifies which credential file to use. Valid values: "ppe", "dev", "admin", "cli".

.EXAMPLE
    # Basic application deployment
    .\Deploy-Application-Template.ps1 -ApplicationName "WebApp" -Version "2.1.0" -Environment "Development"

.EXAMPLE
    # Production deployment with validation
    .\Deploy-Application-Template.ps1 -ApplicationName "WebApp" -Version "2.1.0" -Environment "Production" -ServerList "web01,web02,web03"

.EXAMPLE
    # Upgrade deployment with configuration transformation
    .\Deploy-Application-Template.ps1 -ApplicationName "WebApp" -DeploymentType "Upgrade" -ConfigTransform -EmailRecipients "<EMAIL>"

.EXAMPLE
    # Rollback to previous version
    .\Deploy-Application-Template.ps1 -ApplicationName "WebApp" -DeploymentType "Rollback" -Environment "Production"

.NOTES
    File Name   : Deploy-Application-Template.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in centralized location
    Compatible  : Windows Server 2016, 2019, 2022
    
    Version 1.0 - Initial template for application deployment
    Version 1.1 - Added rollback capabilities and configuration transformation
    Version 1.2 - Added multi-server deployment and validation framework
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$ApplicationName,
    
    [string]$Version,
    
    [ValidateSet("Development", "Staging", "Production")]
    [string]$Environment = "Development",
    
    [string]$ServerList,
    
    [string]$ServerListFile,
    
    [ValidateSet("Fresh", "Upgrade", "Rollback", "Hotfix")]
    [string]$DeploymentType = "Fresh",
    
    [string]$SourcePath,
    
    [string]$BackupLocation,
    
    [switch]$ConfigTransform,
    
    [switch]$SkipPreValidation,
    
    [switch]$SkipPostValidation,
    
    [switch]$Force,
    
    [switch]$DryRun,
    
    [ValidateSet("Console", "JSON", "HTML")]
    [string]$OutputFormat = "Console",
    
    [string]$EmailRecipients,
    
    [switch]$UseStoredCredentials,
    
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
    [ValidateSet("ppe", "dev", "admin", "cli", "")]
    [string]$CredentialTarget = ""
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Resolve-Path "$currentPath\..\..\..\Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Paths ###
$credPath = $config.GlobalConfig.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adminCredLocation = "$credPath\windowsCreds.xml"
$mudCreds = "$credPath\prdDomainCreds.xml"
$ppeCreds = "$credPath\ppeDomainCreds.xml"
$devCreds = "$credPath\devDomainCreds.xml"

### Script Variables ###
$ErrorActionPreference = "Stop"
$dateStamp = Get-Date -Format "yyyy-MM-dd"
$timeStamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"

### Deployment Configuration - Loaded from scriptConfigs.json ###
# TODO: Add DeploymentConfig section to your scriptConfigs.json
$defaultBackupLocation = if ($config.GlobalConfig.ReportPath) { "$($config.GlobalConfig.ReportPath)\Backups" } else { "C:\Backups" }
$logPath = "$defaultBackupLocation\Deployment_$($ApplicationName)_$dateStamp.log"

### Set default values from config if not provided ###
if (-not $BackupLocation) {
    $BackupLocation = $defaultBackupLocation
}

# TODO: Load application-specific configuration
# $appConfig = $config.DeploymentConfig.Applications.$ApplicationName

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
    
    $objectReturn = @{
        applicationName  = $ApplicationName
        version         = $Version
        environment     = $Environment
        deploymentType  = $DeploymentType
        timestamp       = $timeStamp
        serverCount     = if ($script:serverList) { $script:serverList.Count } else { 1 }
        dryRun          = $DryRun.IsPresent
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $logPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-DeploymentCredentials {
    Write-Log "Getting credentials for deployment..."
    
    if ($UseStoredCredentials) {
        try {
            $credFile = $mudCreds
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            } else {
                # Auto-select based on environment
                switch ($Environment.ToLower()) {
                    "development" { $credFile = $devCreds }
                    "staging" { $credFile = $ppeCreds }
                    "production" { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials for user: $($storedCred.UserName)"
                return $storedCred
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    return $null
}

function Get-ServerList {
    Write-Log "Building server list for deployment..."
    
    $servers = @()
    
    if ($ServerListFile -and (Test-Path $ServerListFile)) {
        $servers = Get-Content $ServerListFile | Where-Object { $_.Trim() -ne "" -and -not $_.StartsWith("#") }
        Write-Log "Loaded $($servers.Count) servers from file: $ServerListFile"
    } elseif ($ServerList) {
        $servers = $ServerList -split "," | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
        Write-Log "Using provided server list: $($servers.Count) servers"
    } else {
        # TODO: Load servers from application configuration
        $servers = @($env:COMPUTERNAME)
        Write-Log "Using local system only (no server list provided)"
    }
    
    return $servers
}

function Test-PreDeploymentValidation {
    param([string]$ServerName = $env:COMPUTERNAME)
    
    if ($SkipPreValidation) {
        Write-Log "Skipping pre-deployment validation as requested"
        return @{ Status = "Skipped"; Message = "Pre-validation skipped by user request" }
    }
    
    Write-Log "Performing pre-deployment validation for: $ServerName"
    
    $validationResults = @{
        Status = "Passed"
        Issues = @()
        Checks = @{}
    }
    
    try {
        # Check disk space
        $systemDrive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'" -ComputerName $ServerName
        $freeSpaceGB = [math]::Round($systemDrive.FreeSpace / 1GB, 2)
        $validationResults.Checks.DiskSpace = @{
            FreeSpaceGB = $freeSpaceGB
            Status = if ($freeSpaceGB -gt 5) { "Passed" } else { "Failed" }
        }
        
        if ($freeSpaceGB -le 5) {
            $validationResults.Issues += "Insufficient disk space: $freeSpaceGB GB available (minimum 5GB required)"
            $validationResults.Status = "Failed"
        }
        
        # Check if application is currently running
        # TODO: Add application-specific process/service checks
        $validationResults.Checks.ApplicationRunning = @{
            Status = "Passed"
            Message = "Application status check not implemented"
        }
        
        # Check prerequisites
        # TODO: Add application-specific prerequisite checks
        $validationResults.Checks.Prerequisites = @{
            Status = "Passed"
            Message = "Prerequisites check not implemented"
        }
        
        Write-Log "Pre-deployment validation completed for $ServerName`: $($validationResults.Status)"
        return $validationResults
        
    } catch {
        Write-Log "Error during pre-deployment validation for $ServerName`: $($_.Exception.Message)" -Level "ERROR"
        $validationResults.Status = "Error"
        $validationResults.Issues += "Validation error: $($_.Exception.Message)"
        return $validationResults
    }
}

function Backup-CurrentVersion {
    param(
        [string]$ServerName = $env:COMPUTERNAME,
        [string]$ApplicationPath
    )
    
    Write-Log "Creating backup of current version on: $ServerName"
    
    try {
        $backupPath = "$BackupLocation\$ApplicationName\$ServerName\$dateStamp"
        
        if ($DryRun) {
            Write-Log "DRY RUN: Would create backup at: $backupPath"
            return @{ Status = "Success"; BackupPath = $backupPath; Message = "Dry run - no actual backup created" }
        }
        
        # TODO: Implement actual backup logic
        # - Stop services
        # - Copy application files
        # - Backup configuration
        # - Backup database (if applicable)
        
        Write-Log "Backup completed successfully: $backupPath"
        return @{ Status = "Success"; BackupPath = $backupPath; Message = "Backup created successfully" }
        
    } catch {
        Write-Log "Error creating backup for $ServerName`: $($_.Exception.Message)" -Level "ERROR"
        return @{ Status = "Failed"; Error = $_.Exception.Message }
    }
}

function Deploy-ApplicationFiles {
    param(
        [string]$ServerName = $env:COMPUTERNAME,
        [string]$SourcePath,
        [string]$DestinationPath
    )
    
    Write-Log "Deploying application files to: $ServerName"
    
    try {
        if ($DryRun) {
            Write-Log "DRY RUN: Would deploy from $SourcePath to $DestinationPath on $ServerName"
            return @{ Status = "Success"; Message = "Dry run - no actual deployment performed" }
        }
        
        # TODO: Implement actual deployment logic
        # - Copy application files
        # - Update configuration files
        # - Apply configuration transformations
        # - Update permissions
        # - Register services/components
        
        Write-Log "Application files deployed successfully to $ServerName"
        return @{ Status = "Success"; Message = "Files deployed successfully" }
        
    } catch {
        Write-Log "Error deploying files to $ServerName`: $($_.Exception.Message)" -Level "ERROR"
        return @{ Status = "Failed"; Error = $_.Exception.Message }
    }
}

function Test-PostDeploymentValidation {
    param([string]$ServerName = $env:COMPUTERNAME)
    
    if ($SkipPostValidation) {
        Write-Log "Skipping post-deployment validation as requested"
        return @{ Status = "Skipped"; Message = "Post-validation skipped by user request" }
    }
    
    Write-Log "Performing post-deployment validation for: $ServerName"
    
    $validationResults = @{
        Status = "Passed"
        Issues = @()
        Checks = @{}
    }
    
    try {
        # Check if application is running
        # TODO: Add application-specific health checks
        $validationResults.Checks.ApplicationHealth = @{
            Status = "Passed"
            Message = "Application health check not implemented"
        }
        
        # Check service status
        # TODO: Add service status validation
        $validationResults.Checks.ServiceStatus = @{
            Status = "Passed"
            Message = "Service status check not implemented"
        }
        
        # Check application functionality
        # TODO: Add functional tests
        $validationResults.Checks.FunctionalTest = @{
            Status = "Passed"
            Message = "Functional test not implemented"
        }
        
        Write-Log "Post-deployment validation completed for $ServerName`: $($validationResults.Status)"
        return $validationResults
        
    } catch {
        Write-Log "Error during post-deployment validation for $ServerName`: $($_.Exception.Message)" -Level "ERROR"
        $validationResults.Status = "Error"
        $validationResults.Issues += "Validation error: $($_.Exception.Message)"
        return $validationResults
    }
}

function Invoke-Rollback {
    param(
        [string]$ServerName = $env:COMPUTERNAME,
        [string]$BackupPath
    )
    
    Write-Log "Initiating rollback for: $ServerName"
    
    try {
        if ($DryRun) {
            Write-Log "DRY RUN: Would rollback from backup: $BackupPath"
            return @{ Status = "Success"; Message = "Dry run - no actual rollback performed" }
        }
        
        # TODO: Implement rollback logic
        # - Stop services
        # - Restore application files
        # - Restore configuration
        # - Restore database (if applicable)
        # - Start services
        
        Write-Log "Rollback completed successfully for $ServerName"
        return @{ Status = "Success"; Message = "Rollback completed successfully" }
        
    } catch {
        Write-Log "Error during rollback for $ServerName`: $($_.Exception.Message)" -Level "ERROR"
        return @{ Status = "Failed"; Error = $_.Exception.Message }
    }
}

# TODO: Add additional functions:
# - Transform-ConfigurationFiles
# - Start-ApplicationServices
# - Stop-ApplicationServices
# - Send-DeploymentNotification
# - Generate-DeploymentReport

### Main Execution ###
try {
    Write-Log "=== Application Deployment Started ==="
    Write-Log "Application: $ApplicationName"
    Write-Log "Version: $Version"
    Write-Log "Environment: $Environment"
    Write-Log "Deployment Type: $DeploymentType"
    Write-Log "Dry Run: $($DryRun.IsPresent)"
    
    # Get server list
    $script:serverList = Get-ServerList
    Write-Log "Deploying to $($script:serverList.Count) server(s)"
    
    # Get credentials if needed
    $credentials = Get-DeploymentCredentials
    
    # Initialize deployment results
    $deploymentResults = @{
        OverallStatus = "Success"
        ServerResults = @{}
        Summary = @{
            TotalServers = $script:serverList.Count
            SuccessfulDeployments = 0
            FailedDeployments = 0
            RolledBackDeployments = 0
        }
    }
    
    # Process each server
    foreach ($server in $script:serverList) {
        Write-Log "Processing deployment for server: $server"
        
        $serverResult = @{
            ServerName = $server
            Status = "InProgress"
            Steps = @{}
            StartTime = Get-Date
        }
        
        try {
            # Step 1: Pre-deployment validation
            $preValidation = Test-PreDeploymentValidation -ServerName $server
            $serverResult.Steps.PreValidation = $preValidation
            
            if ($preValidation.Status -eq "Failed" -and -not $Force) {
                throw "Pre-deployment validation failed: $($preValidation.Issues -join '; ')"
            }
            
            # Step 2: Backup current version
            $backup = Backup-CurrentVersion -ServerName $server
            $serverResult.Steps.Backup = $backup
            
            if ($backup.Status -eq "Failed") {
                throw "Backup failed: $($backup.Error)"
            }
            
            # Step 3: Deploy application
            $deployment = Deploy-ApplicationFiles -ServerName $server -SourcePath $SourcePath
            $serverResult.Steps.Deployment = $deployment
            
            if ($deployment.Status -eq "Failed") {
                throw "Deployment failed: $($deployment.Error)"
            }
            
            # Step 4: Post-deployment validation
            $postValidation = Test-PostDeploymentValidation -ServerName $server
            $serverResult.Steps.PostValidation = $postValidation
            
            if ($postValidation.Status -eq "Failed") {
                # Attempt rollback
                Write-Log "Post-deployment validation failed, attempting rollback..." -Level "WARNING"
                $rollback = Invoke-Rollback -ServerName $server -BackupPath $backup.BackupPath
                $serverResult.Steps.Rollback = $rollback
                
                if ($rollback.Status -eq "Success") {
                    $serverResult.Status = "RolledBack"
                    $deploymentResults.Summary.RolledBackDeployments++
                } else {
                    $serverResult.Status = "Failed"
                    $deploymentResults.Summary.FailedDeployments++
                }
                
                $deploymentResults.OverallStatus = "Warning"
            } else {
                $serverResult.Status = "Success"
                $deploymentResults.Summary.SuccessfulDeployments++
            }
            
        } catch {
            Write-Log "Deployment failed for $server`: $($_.Exception.Message)" -Level "ERROR"
            $serverResult.Status = "Failed"
            $serverResult.Error = $_.Exception.Message
            $deploymentResults.Summary.FailedDeployments++
            $deploymentResults.OverallStatus = "Failed"
        }
        
        $serverResult.EndTime = Get-Date
        $serverResult.Duration = ($serverResult.EndTime - $serverResult.StartTime).TotalMinutes
        $deploymentResults.ServerResults[$server] = $serverResult
    }
    
    # Generate summary
    $successRate = [math]::Round(($deploymentResults.Summary.SuccessfulDeployments / $deploymentResults.Summary.TotalServers) * 100, 2)
    $message = "Deployment completed. Success rate: $successRate% ($($deploymentResults.Summary.SuccessfulDeployments)/$($deploymentResults.Summary.TotalServers))"
    
    Write-Log $message
    Write-Log "=== Application Deployment Completed ==="
    
    # Output results based on format
    switch ($OutputFormat) {
        "Console" {
            Write-Host "`n=== DEPLOYMENT RESULTS ===" -ForegroundColor Cyan
            Write-Host "Overall Status: $($deploymentResults.OverallStatus)" -ForegroundColor $(
                switch ($deploymentResults.OverallStatus) {
                    "Success" { "Green" }
                    "Warning" { "Yellow" }
                    "Failed" { "Red" }
                    default { "White" }
                }
            )
            Write-Host "Success Rate: $successRate%"
            Write-Host "Successful: $($deploymentResults.Summary.SuccessfulDeployments)"
            Write-Host "Failed: $($deploymentResults.Summary.FailedDeployments)"
            Write-Host "Rolled Back: $($deploymentResults.Summary.RolledBackDeployments)"
        }
        default {
            $jsonResult = New-JsonReturn -Success "true" -Status $deploymentResults.OverallStatus -Message $message -Data $deploymentResults
            Write-Output $jsonResult
        }
    }
    
} catch {
    Write-Log "=== Application Deployment Failed ===" -Level "ERROR"
    Write-Log "Error: $($_.Exception.Message)" -Level "ERROR"
    
    $errorResponse = New-JsonReturn -Success "false" -Status "failed" -Message "Deployment failed: $($_.Exception.Message)"
    Write-Output $errorResponse
    
    exit 1
}
