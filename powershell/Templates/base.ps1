<#
    .SYNOPSIS
    A synopsis of the script.

    .DESCRIPTION
    Describe what the script does.

    .PARAMETER parameter
    List all parameters and their descriptions.

    .EXAMPLE
    An example of how to use the script.

    .NOTES
    File Name: script.ps1
    Author: <PERSON><PERSON>
    Version 1.0 - 03/08/2022

    This function requires an environment variable called 'OfflineMount' to be defined beforehand.
    This can be done by typing: '$env:OfflineMount = "D:\Mount"' or you can specify the $OfflineMountFolder variable.
#>
#requires -RunAsAdministrator
#requires -Version 5
#requires -Modules Dism

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
change-directory $currentPath
$config = Get-Content -Path "..\..\Configs\scriptConfigs.json" | ConvertFrom-Json
$dateStamp = Get-Date -Format yyyy-MM-dd
$timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

### Paths ##
$appSource = $config.AppSourcePath
$credPath = $config.CredPath
$scriptPath = $config.ScriptPath
$reportPath = $config.ReportPath
$adminCredLocation = "$credPath\windowsCreds.xml"
$domainCreds = "$credPath\domainCreds.xml"

#SecureCredentials
$Cred = Get-Credential 
$encryptedSCVMM = get-content C:\scripts\secret.txt
$userSCVMM = "mud\svcscvmmadmin"
$passwordSCVMM = ConvertTo-SecureString -string $encryptedSCVMM
$credSCVMM = New-Object -typename System.Management.Automation.PSCredential -argumentlist $userSCVMM,$passwordSCVMM

