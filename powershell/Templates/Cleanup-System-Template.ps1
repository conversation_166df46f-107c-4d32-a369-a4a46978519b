<#
.SYNOPSIS
    Performs comprehensive system cleanup and maintenance tasks on Windows systems.

.DESCRIPTION
    This script performs various system cleanup and maintenance operations including:
    1. Temporary file removal (Windows temp, user temp, browser cache)
    2. Log file rotation and cleanup
    3. System cache clearing (DNS, ARP, NetBIOS)
    4. Registry cleanup and optimization
    5. Disk space optimization and defragmentation
    6. Event log management and archiving
    7. Recycle bin cleanup and old file removal
    
    The script supports both local and remote execution with detailed reporting,
    safety checks, and rollback capabilities. All configuration values are loaded
    from the centralized scriptConfigs.json file.

.PARAMETER ServerList
    Comma-separated list of servers to clean up. If not specified, runs on local system.

.PARAMETER ServerListFile
    Path to a text file containing server names (one per line).

.PARAMETER CleanupType
    Type of cleanup to perform. Valid values: "Basic", "Temp", "Logs", "Cache", "Registry", "Disk", "Full". Default is "Basic".

.PARAMETER TempFileAge
    Age in days for temporary files to be considered for deletion. Default is 7 days.

.PARAMETER LogFileAge
    Age in days for log files to be considered for rotation/deletion. Default is 30 days.

.PARAMETER MaxLogSize
    Maximum size in MB for individual log files before rotation. Default is 100MB.

.PARAMETER FreeSpaceThreshold
    Minimum free space percentage to trigger aggressive cleanup. Default is 10%.

.PARAMETER ExcludePaths
    Comma-separated list of paths to exclude from cleanup operations.

.PARAMETER DryRun
    Perform a dry run without making actual changes - shows what would be cleaned.

.PARAMETER Force
    Force cleanup operations without confirmation prompts.

.PARAMETER OutputFormat
    Output format for cleanup results. Valid values: "Console", "JSON", "CSV", "HTML". Default is "Console".

.PARAMETER OutputPath
    Path where reports will be saved. Default value is loaded from scriptConfigs.json.

.PARAMETER EmailRecipients
    Comma-separated list of email recipients for cleanup reports.

.PARAMETER ScheduleCleanup
    Create scheduled task for automatic cleanup.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for remote operations.

.PARAMETER CredentialTarget
    Specifies which credential file to use. Valid values: "ppe", "dev", "admin", "cli".

.EXAMPLE
    # Basic cleanup on local system
    .\Cleanup-System-Template.ps1

.EXAMPLE
    # Full cleanup with dry run
    .\Cleanup-System-Template.ps1 -CleanupType "Full" -DryRun

.EXAMPLE
    # Temporary files cleanup on multiple servers
    .\Cleanup-System-Template.ps1 -ServerList "server01,server02,server03" -CleanupType "Temp" -TempFileAge 3

.EXAMPLE
    # Log cleanup with custom retention
    .\Cleanup-System-Template.ps1 -CleanupType "Logs" -LogFileAge 14 -MaxLogSize 50 -OutputFormat "JSON"

.NOTES
    File Name   : Cleanup-System-Template.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in centralized location
    Compatible  : Windows Server 2016, 2019, 2022, Windows 10/11
    
    Version 1.0 - Initial template for system cleanup
    Version 1.1 - Added log rotation and registry cleanup
    Version 1.2 - Added scheduling and remote execution capabilities
#>

[CmdletBinding()]
param(
    [string]$ServerList,
    
    [string]$ServerListFile,
    
    [ValidateSet("Basic", "Temp", "Logs", "Cache", "Registry", "Disk", "Full")]
    [string]$CleanupType = "Basic",
    
    [ValidateRange(1, 365)]
    [int]$TempFileAge = 7,
    
    [ValidateRange(1, 365)]
    [int]$LogFileAge = 30,
    
    [ValidateRange(1, 1000)]
    [int]$MaxLogSize = 100,
    
    [ValidateRange(1, 50)]
    [int]$FreeSpaceThreshold = 10,
    
    [string]$ExcludePaths,
    
    [switch]$DryRun,
    
    [switch]$Force,
    
    [ValidateSet("Console", "JSON", "CSV", "HTML")]
    [string]$OutputFormat = "Console",
    
    [string]$OutputPath,
    
    [string]$EmailRecipients,
    
    [switch]$ScheduleCleanup,
    
    [switch]$UseStoredCredentials,
    
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
    [ValidateSet("ppe", "dev", "admin", "cli", "")]
    [string]$CredentialTarget = ""
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Resolve-Path "$currentPath\..\..\..\Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Paths ###
$credPath = $config.GlobalConfig.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adminCredLocation = "$credPath\windowsCreds.xml"
$mudCreds = "$credPath\prdDomainCreds.xml"
$ppeCreds = "$credPath\ppeDomainCreds.xml"
$devCreds = "$credPath\devDomainCreds.xml"

### Script Variables ###
$ErrorActionPreference = "Continue"  # Continue on errors for cleanup operations
$dateStamp = Get-Date -Format "yyyy-MM-dd"
$timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

### Cleanup Configuration - Loaded from scriptConfigs.json ###
# TODO: Add CleanupConfig section to your scriptConfigs.json
$defaultOutputPath = if ($config.GlobalConfig.ReportPath) { $config.GlobalConfig.ReportPath } else { "C:\Reports" }
$logPath = "$defaultOutputPath\SystemCleanup_$dateStamp.log"

### Set default values from config if not provided ###
if (-not $OutputPath) {
    $OutputPath = $defaultOutputPath
}

# Parse exclude paths
$excludePathList = if ($ExcludePaths) { $ExcludePaths -split "," | ForEach-Object { $_.Trim() } } else { @() }

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
    
    $objectReturn = @{
        cleanupType     = $CleanupType
        serverCount     = if ($script:serverList) { $script:serverList.Count } else { 1 }
        timestamp       = $timeStamp
        dryRun          = $DryRun.IsPresent
        settings        = @{
            tempFileAge         = $TempFileAge
            logFileAge          = $LogFileAge
            maxLogSize          = $MaxLogSize
            freeSpaceThreshold  = $FreeSpaceThreshold
        }
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $logPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-CleanupCredentials {
    Write-Log "Getting credentials for cleanup operations..."
    
    if ($UseStoredCredentials) {
        try {
            $credFile = $mudCreds
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials for user: $($storedCred.UserName)"
                return $storedCred
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    return $null
}

function Get-ServerList {
    Write-Log "Building server list for cleanup..."
    
    $servers = @()
    
    if ($ServerListFile -and (Test-Path $ServerListFile)) {
        $servers = Get-Content $ServerListFile | Where-Object { $_.Trim() -ne "" -and -not $_.StartsWith("#") }
        Write-Log "Loaded $($servers.Count) servers from file: $ServerListFile"
    } elseif ($ServerList) {
        $servers = $ServerList -split "," | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
        Write-Log "Using provided server list: $($servers.Count) servers"
    } else {
        $servers = @($env:COMPUTERNAME)
        Write-Log "Using local system only"
    }
    
    return $servers
}

function Get-DiskSpaceInfo {
    param([string]$ComputerName = $env:COMPUTERNAME)
    
    try {
        $disks = Get-WmiObject -Class Win32_LogicalDisk -Filter "DriveType=3" -ComputerName $ComputerName
        $diskInfo = @{}
        
        foreach ($disk in $disks) {
            $freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
            $totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
            $freeSpacePercent = [math]::Round(($disk.FreeSpace / $disk.Size) * 100, 2)
            
            $diskInfo[$disk.DeviceID] = @{
                TotalSpaceGB = $totalSpaceGB
                FreeSpaceGB = $freeSpaceGB
                FreeSpacePercent = $freeSpacePercent
                Status = if ($freeSpacePercent -lt $FreeSpaceThreshold) { "Critical" } elseif ($freeSpacePercent -lt 20) { "Warning" } else { "OK" }
            }
        }
        
        return $diskInfo
        
    } catch {
        Write-Log "Error getting disk space info for $ComputerName`: $($_.Exception.Message)" -Level "ERROR"
        return @{}
    }
}

function Clear-TemporaryFiles {
    param([string]$ComputerName = $env:COMPUTERNAME)
    
    if ($CleanupType -notin @("Temp", "Full")) {
        return @{ Status = "Skipped"; Message = "Temporary file cleanup not requested" }
    }
    
    Write-Log "Cleaning temporary files on: $ComputerName"
    
    $cleanupResults = @{
        Status = "Success"
        FilesRemoved = 0
        SpaceFreedMB = 0
        Locations = @{}
        Issues = @()
    }
    
    try {
        $cutoffDate = (Get-Date).AddDays(-$TempFileAge)
        
        # Define temp locations to clean
        $tempLocations = @(
            "C:\Windows\Temp",
            "C:\Windows\Prefetch",
            "C:\Users\<USER>\AppData\Local\Temp",
            "C:\Users\<USER>\AppData\Local\Microsoft\Windows\Temporary Internet Files",
            "C:\Windows\SoftwareDistribution\Download"
        )
        
        foreach ($location in $tempLocations) {
            try {
                if ($location -like "*`**") {
                    # Handle wildcard paths
                    $expandedPaths = Get-ChildItem -Path ($location -replace '\*', '') -Directory -ErrorAction SilentlyContinue | 
                                   ForEach-Object { $location -replace '\*', $_.Name }
                } else {
                    $expandedPaths = @($location)
                }
                
                foreach ($path in $expandedPaths) {
                    if (Test-Path $path) {
                        # Skip excluded paths
                        $isExcluded = $false
                        foreach ($excludePath in $excludePathList) {
                            if ($path -like "*$excludePath*") {
                                $isExcluded = $true
                                break
                            }
                        }
                        
                        if ($isExcluded) {
                            Write-Log "Skipping excluded path: $path"
                            continue
                        }
                        
                        $beforeSize = (Get-ChildItem -Path $path -Recurse -File -ErrorAction SilentlyContinue | 
                                     Where-Object { $_.LastWriteTime -lt $cutoffDate } | 
                                     Measure-Object -Property Length -Sum).Sum
                        
                        if ($DryRun) {
                            $fileCount = (Get-ChildItem -Path $path -Recurse -File -ErrorAction SilentlyContinue | 
                                        Where-Object { $_.LastWriteTime -lt $cutoffDate }).Count
                            Write-Log "DRY RUN: Would remove $fileCount files from $path ($(([math]::Round($beforeSize / 1MB, 2))) MB)"
                        } else {
                            Get-ChildItem -Path $path -Recurse -File -ErrorAction SilentlyContinue | 
                            Where-Object { $_.LastWriteTime -lt $cutoffDate } | 
                            Remove-Item -Force -ErrorAction SilentlyContinue
                            
                            $afterSize = (Get-ChildItem -Path $path -Recurse -File -ErrorAction SilentlyContinue | 
                                        Measure-Object -Property Length -Sum).Sum
                            
                            $spaceFreed = $beforeSize - $afterSize
                            $cleanupResults.SpaceFreedMB += [math]::Round($spaceFreed / 1MB, 2)
                        }
                        
                        $cleanupResults.Locations[$path] = @{
                            SpaceFreedMB = if ($DryRun) { [math]::Round($beforeSize / 1MB, 2) } else { [math]::Round(($beforeSize - $afterSize) / 1MB, 2) }
                            Status = "Cleaned"
                        }
                    }
                }
                
            } catch {
                $cleanupResults.Issues += "Error cleaning $location`: $($_.Exception.Message)"
                Write-Log "Error cleaning $location`: $($_.Exception.Message)" -Level "WARNING"
            }
        }
        
        Write-Log "Temporary file cleanup completed for $ComputerName. Space freed: $($cleanupResults.SpaceFreedMB) MB"
        return $cleanupResults
        
    } catch {
        Write-Log "Error during temporary file cleanup for $ComputerName`: $($_.Exception.Message)" -Level "ERROR"
        $cleanupResults.Status = "Error"
        $cleanupResults.Issues += "General cleanup error: $($_.Exception.Message)"
        return $cleanupResults
    }
}

function Clear-SystemLogs {
    param([string]$ComputerName = $env:COMPUTERNAME)
    
    if ($CleanupType -notin @("Logs", "Full")) {
        return @{ Status = "Skipped"; Message = "Log cleanup not requested" }
    }
    
    Write-Log "Cleaning system logs on: $ComputerName"
    
    $logResults = @{
        Status = "Success"
        LogsProcessed = 0
        SpaceFreedMB = 0
        Actions = @{}
        Issues = @()
    }
    
    try {
        # Get event logs
        $eventLogs = Get-WinEvent -ListLog * -ErrorAction SilentlyContinue | 
                    Where-Object { $_.RecordCount -gt 0 -and $_.LogFilePath }
        
        foreach ($log in $eventLogs) {
            try {
                $logFile = $log.LogFilePath
                if (Test-Path $logFile) {
                    $logSize = (Get-Item $logFile).Length
                    $logSizeMB = [math]::Round($logSize / 1MB, 2)
                    
                    if ($logSizeMB -gt $MaxLogSize) {
                        if ($DryRun) {
                            Write-Log "DRY RUN: Would clear log '$($log.LogName)' ($logSizeMB MB)"
                            $logResults.Actions[$log.LogName] = "Would clear (size: $logSizeMB MB)"
                        } else {
                            # Archive log before clearing
                            $archivePath = "$OutputPath\LogArchives\$($log.LogName)_$dateStamp.evtx"
                            New-Item -Path (Split-Path $archivePath) -ItemType Directory -Force -ErrorAction SilentlyContinue
                            
                            wevtutil epl $log.LogName $archivePath
                            wevtutil cl $log.LogName
                            
                            $logResults.SpaceFreedMB += $logSizeMB
                            $logResults.Actions[$log.LogName] = "Cleared and archived (freed: $logSizeMB MB)"
                        }
                        
                        $logResults.LogsProcessed++
                    }
                }
                
            } catch {
                $logResults.Issues += "Error processing log '$($log.LogName)': $($_.Exception.Message)"
                Write-Log "Error processing log '$($log.LogName)': $($_.Exception.Message)" -Level "WARNING"
            }
        }
        
        Write-Log "Log cleanup completed for $ComputerName. Logs processed: $($logResults.LogsProcessed), Space freed: $($logResults.SpaceFreedMB) MB"
        return $logResults
        
    } catch {
        Write-Log "Error during log cleanup for $ComputerName`: $($_.Exception.Message)" -Level "ERROR"
        $logResults.Status = "Error"
        $logResults.Issues += "General log cleanup error: $($_.Exception.Message)"
        return $logResults
    }
}

function Clear-SystemCache {
    param([string]$ComputerName = $env:COMPUTERNAME)
    
    if ($CleanupType -notin @("Cache", "Full")) {
        return @{ Status = "Skipped"; Message = "Cache cleanup not requested" }
    }
    
    Write-Log "Clearing system cache on: $ComputerName"
    
    $cacheResults = @{
        Status = "Success"
        CachesCleared = @()
        Issues = @()
    }
    
    try {
        if ($DryRun) {
            Write-Log "DRY RUN: Would clear DNS cache, ARP cache, and NetBIOS cache"
            $cacheResults.CachesCleared = @("DNS (dry run)", "ARP (dry run)", "NetBIOS (dry run)")
        } else {
            # Clear DNS cache
            try {
                ipconfig /flushdns | Out-Null
                $cacheResults.CachesCleared += "DNS"
                Write-Log "DNS cache cleared"
            } catch {
                $cacheResults.Issues += "Failed to clear DNS cache: $($_.Exception.Message)"
            }
            
            # Clear ARP cache
            try {
                arp -d * 2>$null
                $cacheResults.CachesCleared += "ARP"
                Write-Log "ARP cache cleared"
            } catch {
                $cacheResults.Issues += "Failed to clear ARP cache: $($_.Exception.Message)"
            }
            
            # Clear NetBIOS cache
            try {
                nbtstat -R 2>$null
                nbtstat -RR 2>$null
                $cacheResults.CachesCleared += "NetBIOS"
                Write-Log "NetBIOS cache cleared"
            } catch {
                $cacheResults.Issues += "Failed to clear NetBIOS cache: $($_.Exception.Message)"
            }
        }
        
        Write-Log "Cache cleanup completed for $ComputerName. Caches cleared: $($cacheResults.CachesCleared -join ', ')"
        return $cacheResults
        
    } catch {
        Write-Log "Error during cache cleanup for $ComputerName`: $($_.Exception.Message)" -Level "ERROR"
        $cacheResults.Status = "Error"
        $cacheResults.Issues += "General cache cleanup error: $($_.Exception.Message)"
        return $cacheResults
    }
}

# TODO: Add additional cleanup functions:
# - Clear-RegistryCleanup
# - Optimize-DiskSpace
# - Clear-RecycleBin
# - Remove-OldFiles
# - Create-CleanupSchedule

### Main Execution ###
try {
    Write-Log "=== System Cleanup Started ==="
    Write-Log "Cleanup Type: $CleanupType"
    Write-Log "Dry Run: $($DryRun.IsPresent)"
    
    # Get server list
    $script:serverList = Get-ServerList
    Write-Log "Cleaning $($script:serverList.Count) server(s)"
    
    # Get credentials if needed
    $credentials = Get-CleanupCredentials
    
    # Initialize cleanup results
    $allResults = @{
        OverallStatus = "Success"
        ServerResults = @{}
        Summary = @{
            TotalServers = $script:serverList.Count
            TotalSpaceFreedMB = 0
            TotalFilesRemoved = 0
            TotalLogsProcessed = 0
        }
    }
    
    # Process each server
    foreach ($server in $script:serverList) {
        Write-Log "Processing cleanup for server: $server"
        
        $serverResult = @{
            ServerName = $server
            Status = "Success"
            StartTime = Get-Date
            DiskSpaceBefore = Get-DiskSpaceInfo -ComputerName $server
            CleanupResults = @{}
        }
        
        try {
            # Perform cleanup operations based on type
            if ($CleanupType -in @("Basic", "Temp", "Full")) {
                $tempResult = Clear-TemporaryFiles -ComputerName $server
                $serverResult.CleanupResults.TemporaryFiles = $tempResult
                $allResults.Summary.TotalSpaceFreedMB += $tempResult.SpaceFreedMB
            }
            
            if ($CleanupType -in @("Logs", "Full")) {
                $logResult = Clear-SystemLogs -ComputerName $server
                $serverResult.CleanupResults.SystemLogs = $logResult
                $allResults.Summary.TotalSpaceFreedMB += $logResult.SpaceFreedMB
                $allResults.Summary.TotalLogsProcessed += $logResult.LogsProcessed
            }
            
            if ($CleanupType -in @("Cache", "Full")) {
                $cacheResult = Clear-SystemCache -ComputerName $server
                $serverResult.CleanupResults.SystemCache = $cacheResult
            }
            
            # Get disk space after cleanup
            $serverResult.DiskSpaceAfter = Get-DiskSpaceInfo -ComputerName $server
            
            # Calculate space improvement
            foreach ($drive in $serverResult.DiskSpaceBefore.Keys) {
                if ($serverResult.DiskSpaceAfter.ContainsKey($drive)) {
                    $spaceImprovement = $serverResult.DiskSpaceAfter[$drive].FreeSpaceGB - $serverResult.DiskSpaceBefore[$drive].FreeSpaceGB
                    $serverResult.DiskSpaceAfter[$drive].SpaceImprovement = [math]::Round($spaceImprovement, 2)
                }
            }
            
        } catch {
            Write-Log "Cleanup failed for $server`: $($_.Exception.Message)" -Level "ERROR"
            $serverResult.Status = "Failed"
            $serverResult.Error = $_.Exception.Message
            $allResults.OverallStatus = "Warning"
        }
        
        $serverResult.EndTime = Get-Date
        $serverResult.Duration = ($serverResult.EndTime - $serverResult.StartTime).TotalMinutes
        $allResults.ServerResults[$server] = $serverResult
    }
    
    $message = "System cleanup completed. Total space freed: $($allResults.Summary.TotalSpaceFreedMB) MB across $($allResults.Summary.TotalServers) server(s)"
    Write-Log $message
    
    # Output results based on format
    switch ($OutputFormat) {
        "Console" {
            Write-Host "`n=== SYSTEM CLEANUP RESULTS ===" -ForegroundColor Cyan
            Write-Host "Overall Status: $($allResults.OverallStatus)" -ForegroundColor $(if ($allResults.OverallStatus -eq "Success") { "Green" } else { "Yellow" })
            Write-Host "Total Space Freed: $($allResults.Summary.TotalSpaceFreedMB) MB"
            Write-Host "Logs Processed: $($allResults.Summary.TotalLogsProcessed)"
            
            foreach ($server in $allResults.ServerResults.Keys) {
                $result = $allResults.ServerResults[$server]
                Write-Host "`n$server`: $($result.Status)" -ForegroundColor $(if ($result.Status -eq "Success") { "Green" } else { "Red" })
                
                foreach ($drive in $result.DiskSpaceAfter.Keys) {
                    $improvement = $result.DiskSpaceAfter[$drive].SpaceImprovement
                    if ($improvement -gt 0) {
                        Write-Host "  Drive $drive`: +$improvement GB freed" -ForegroundColor Green
                    }
                }
            }
        }
        default {
            $reportPath = "$OutputPath\SystemCleanup_$($CleanupType)_$(Get-Date -Format 'yyyy-MM-dd_HH-mm-ss')"
            
            switch ($OutputFormat) {
                "JSON" {
                    $allResults | ConvertTo-Json -Depth 4 | Out-File -FilePath "$reportPath.json" -Encoding UTF8
                    Write-Log "JSON report saved to: $reportPath.json"
                }
                "CSV" {
                    # Flatten results for CSV export
                    $csvData = @()
                    foreach ($server in $allResults.ServerResults.Keys) {
                        $result = $allResults.ServerResults[$server]
                        $csvData += [PSCustomObject]@{
                            ServerName = $server
                            Status = $result.Status
                            Duration = $result.Duration
                            TempSpaceFreed = if ($result.CleanupResults.TemporaryFiles) { $result.CleanupResults.TemporaryFiles.SpaceFreedMB } else { 0 }
                            LogSpaceFreed = if ($result.CleanupResults.SystemLogs) { $result.CleanupResults.SystemLogs.SpaceFreedMB } else { 0 }
                            LogsProcessed = if ($result.CleanupResults.SystemLogs) { $result.CleanupResults.SystemLogs.LogsProcessed } else { 0 }
                        }
                    }
                    $csvData | Export-Csv -Path "$reportPath.csv" -NoTypeInformation -Encoding UTF8
                    Write-Log "CSV report saved to: $reportPath.csv"
                }
            }
        }
    }
    
    # Return JSON response
    $jsonResult = New-JsonReturn -Success "true" -Status $allResults.OverallStatus -Message $message -Data $allResults
    
    if ($OutputFormat -eq "JSON") {
        Write-Output $jsonResult
    }
    
    Write-Log "=== System Cleanup Completed ==="
    
} catch {
    Write-Log "=== System Cleanup Failed ===" -Level "ERROR"
    Write-Log "Error: $($_.Exception.Message)" -Level "ERROR"
    
    $errorResponse = New-JsonReturn -Success "false" -Status "failed" -Message "System cleanup failed: $($_.Exception.Message)"
    Write-Output $errorResponse
    
    exit 1
}
