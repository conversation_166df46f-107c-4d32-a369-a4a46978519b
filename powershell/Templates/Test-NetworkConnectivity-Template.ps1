<#
.SYNOPSIS
    Tests network connectivity and validates network infrastructure components.

.DESCRIPTION
    This script performs comprehensive network connectivity testing including:
    1. ICMP ping tests to multiple targets
    2. TCP port connectivity validation
    3. DNS resolution testing
    4. Certificate validation for HTTPS endpoints
    5. Bandwidth and latency measurements
    6. Service availability checks
    7. Network path analysis and troubleshooting
    
    The script supports testing from multiple source systems to multiple targets
    with detailed reporting and alerting capabilities. All configuration values
    are loaded from the centralized scriptConfigs.json file.

.PARAMETER TargetList
    Comma-separated list of targets to test (hostnames, IPs, or URLs).

.PARAMETER TargetListFile
    Path to a text file containing targets (one per line).

.PARAMETER TestType
    Type of connectivity test. Valid values: "Ping", "Port", "DNS", "Certificate", "Bandwidth", "All". Default is "Ping".

.PARAMETER Ports
    Comma-separated list of ports to test. Default is "80,443,3389".

.PARAMETER Timeout
    Timeout in seconds for each test. Default is 5 seconds.

.PARAMETER Count
    Number of ping attempts per target. Default is 4.

.PARAMETER OutputFormat
    Output format for results. Valid values: "Console", "JSO<PERSON>", "CSV", "HTML". Default is "Console".

.PARAMETER OutputPath
    Path where reports will be saved. Default value is loaded from scriptConfigs.json.

.PARAMETER AlertThreshold
    Percentage of failed tests that triggers an alert. Default is 20%.

.PARAMETER EmailRecipients
    Comma-separated list of email recipients for alerts.

.PARAMETER IncludeLatency
    Include detailed latency measurements and statistics.

.PARAMETER IncludeTraceRoute
    Include trace route analysis for failed connections.

.PARAMETER ContinuousMode
    Run tests continuously with specified interval.

.PARAMETER Interval
    Interval in seconds for continuous mode. Default is 300 (5 minutes).

.EXAMPLE
    # Basic ping test to common targets
    .\Test-NetworkConnectivity-Template.ps1 -TargetList "google.com,microsoft.com,github.com"

.EXAMPLE
    # Port connectivity test with custom ports
    .\Test-NetworkConnectivity-Template.ps1 -TestType "Port" -TargetList "server01,server02" -Ports "80,443,1433,3389"

.EXAMPLE
    # Comprehensive test with JSON output
    .\Test-NetworkConnectivity-Template.ps1 -TestType "All" -TargetListFile "C:\Scripts\targets.txt" -OutputFormat "JSON" -OutputPath "C:\Reports"

.EXAMPLE
    # Continuous monitoring with alerts
    .\Test-NetworkConnectivity-Template.ps1 -ContinuousMode -Interval 60 -AlertThreshold 10 -EmailRecipients "<EMAIL>"

.NOTES
    File Name   : Test-NetworkConnectivity-Template.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later
    Requires    : scriptConfigs.json file in centralized location
    Compatible  : Windows Server 2016, 2019, 2022, Windows 10/11
    
    Version 1.0 - Initial template for network connectivity testing
    Version 1.1 - Added certificate validation and bandwidth testing
    Version 1.2 - Added continuous monitoring and alerting
#>

[CmdletBinding()]
param(
    [string]$TargetList,
    
    [string]$TargetListFile,
    
    [ValidateSet("Ping", "Port", "DNS", "Certificate", "Bandwidth", "All")]
    [string]$TestType = "Ping",
    
    [string]$Ports = "80,443,3389",
    
    [ValidateRange(1, 300)]
    [int]$Timeout = 5,
    
    [ValidateRange(1, 100)]
    [int]$Count = 4,
    
    [ValidateSet("Console", "JSON", "CSV", "HTML")]
    [string]$OutputFormat = "Console",
    
    [string]$OutputPath,
    
    [ValidateRange(0, 100)]
    [int]$AlertThreshold = 20,
    
    [string]$EmailRecipients,
    
    [switch]$IncludeLatency,
    
    [switch]$IncludeTraceRoute,
    
    [switch]$ContinuousMode,
    
    [ValidateRange(10, 3600)]
    [int]$Interval = 300
)

#Requires -Version 5

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Resolve-Path "$currentPath\..\..\..\Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Script Variables ###
$ErrorActionPreference = "Continue"
$dateStamp = Get-Date -Format "yyyy-MM-dd"
$timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

### Network Test Configuration - Loaded from scriptConfigs.json ###
# TODO: Add NetworkTestConfig section to your scriptConfigs.json
$defaultOutputPath = if ($config.GlobalConfig.ReportPath) { $config.GlobalConfig.ReportPath } else { "C:\Reports" }
$logPath = "$defaultOutputPath\NetworkTest_$dateStamp.log"

### Set default values from config if not provided ###
if (-not $OutputPath) {
    $OutputPath = $defaultOutputPath
}

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
    
    $objectReturn = @{
        testType     = $TestType
        sourceSystem = $env:COMPUTERNAME
        timestamp    = $timeStamp
        targetCount  = if ($script:targetList) { $script:targetList.Count } else { 0 }
        testSettings = @{
            timeout = $Timeout
            count   = $Count
            ports   = $Ports
        }
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $logPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-TargetList {
    Write-Log "Building target list..."
    
    $targets = @()
    
    if ($TargetListFile -and (Test-Path $TargetListFile)) {
        $targets = Get-Content $TargetListFile | Where-Object { $_.Trim() -ne "" -and -not $_.StartsWith("#") }
        Write-Log "Loaded $($targets.Count) targets from file: $TargetListFile"
    } elseif ($TargetList) {
        $targets = $TargetList -split "," | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
        Write-Log "Using provided target list: $($targets.Count) targets"
    } else {
        # Default targets for testing
        $targets = @("*******", "*******", "google.com", "microsoft.com")
        Write-Log "Using default target list: $($targets.Count) targets"
    }
    
    return $targets
}

function Test-PingConnectivity {
    param(
        [string]$Target,
        [int]$Count = $Count,
        [int]$Timeout = $Timeout
    )
    
    try {
        Write-Log "Testing ping connectivity to: $Target"
        
        $pingResults = @()
        $successCount = 0
        $totalLatency = 0
        
        for ($i = 1; $i -le $Count; $i++) {
            try {
                $ping = Test-Connection -ComputerName $Target -Count 1 -Quiet -TimeoutSeconds $Timeout
                if ($ping) {
                    $pingDetail = Test-Connection -ComputerName $Target -Count 1 -TimeoutSeconds $Timeout
                    $latency = $pingDetail.ResponseTime
                    $successCount++
                    $totalLatency += $latency
                    
                    $pingResults += [PSCustomObject]@{
                        Attempt = $i
                        Status = "Success"
                        Latency = $latency
                        IPAddress = $pingDetail.IPV4Address.IPAddressToString
                    }
                } else {
                    $pingResults += [PSCustomObject]@{
                        Attempt = $i
                        Status = "Failed"
                        Latency = $null
                        IPAddress = $null
                    }
                }
            } catch {
                $pingResults += [PSCustomObject]@{
                    Attempt = $i
                    Status = "Error"
                    Latency = $null
                    IPAddress = $null
                    Error = $_.Exception.Message
                }
            }
        }
        
        $successRate = [math]::Round(($successCount / $Count) * 100, 2)
        $avgLatency = if ($successCount -gt 0) { [math]::Round($totalLatency / $successCount, 2) } else { $null }
        
        return [PSCustomObject]@{
            Target = $Target
            TestType = "Ping"
            SuccessCount = $successCount
            TotalAttempts = $Count
            SuccessRate = $successRate
            AverageLatency = $avgLatency
            Results = $pingResults
            Status = if ($successRate -ge 80) { "Healthy" } elseif ($successRate -ge 50) { "Warning" } else { "Critical" }
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        
    } catch {
        Write-Log "Error testing ping connectivity to $Target`: $($_.Exception.Message)" -Level "ERROR"
        return [PSCustomObject]@{
            Target = $Target
            TestType = "Ping"
            Status = "Error"
            Error = $_.Exception.Message
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
    }
}

function Test-PortConnectivity {
    param(
        [string]$Target,
        [array]$PortList,
        [int]$Timeout = $Timeout
    )
    
    try {
        Write-Log "Testing port connectivity to: $Target"
        
        $portResults = @()
        
        foreach ($port in $PortList) {
            try {
                $tcpClient = New-Object System.Net.Sockets.TcpClient
                $connect = $tcpClient.BeginConnect($Target, $port, $null, $null)
                $wait = $connect.AsyncWaitHandle.WaitOne($Timeout * 1000, $false)
                
                if ($wait) {
                    try {
                        $tcpClient.EndConnect($connect)
                        $portResults += [PSCustomObject]@{
                            Port = $port
                            Status = "Open"
                            ResponseTime = $null  # TODO: Implement response time measurement
                        }
                    } catch {
                        $portResults += [PSCustomObject]@{
                            Port = $port
                            Status = "Closed"
                            Error = $_.Exception.Message
                        }
                    }
                } else {
                    $portResults += [PSCustomObject]@{
                        Port = $port
                        Status = "Timeout"
                    }
                }
                
                $tcpClient.Close()
                
            } catch {
                $portResults += [PSCustomObject]@{
                    Port = $port
                    Status = "Error"
                    Error = $_.Exception.Message
                }
            }
        }
        
        $openPorts = ($portResults | Where-Object { $_.Status -eq "Open" }).Count
        $successRate = [math]::Round(($openPorts / $PortList.Count) * 100, 2)
        
        return [PSCustomObject]@{
            Target = $Target
            TestType = "Port"
            OpenPorts = $openPorts
            TotalPorts = $PortList.Count
            SuccessRate = $successRate
            Results = $portResults
            Status = if ($successRate -ge 80) { "Healthy" } elseif ($successRate -ge 50) { "Warning" } else { "Critical" }
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        
    } catch {
        Write-Log "Error testing port connectivity to $Target`: $($_.Exception.Message)" -Level "ERROR"
        return [PSCustomObject]@{
            Target = $Target
            TestType = "Port"
            Status = "Error"
            Error = $_.Exception.Message
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
    }
}

function Test-DNSResolution {
    param(
        [string]$Target,
        [int]$Timeout = $Timeout
    )
    
    try {
        Write-Log "Testing DNS resolution for: $Target"
        
        $dnsResults = @()
        $startTime = Get-Date
        
        try {
            $dnsResult = Resolve-DnsName -Name $Target -ErrorAction Stop
            $endTime = Get-Date
            $responseTime = ($endTime - $startTime).TotalMilliseconds
            
            foreach ($record in $dnsResult) {
                $dnsResults += [PSCustomObject]@{
                    Name = $record.Name
                    Type = $record.Type
                    IPAddress = $record.IPAddress
                    TTL = $record.TTL
                }
            }
            
            return [PSCustomObject]@{
                Target = $Target
                TestType = "DNS"
                Status = "Success"
                ResponseTime = [math]::Round($responseTime, 2)
                RecordCount = $dnsResults.Count
                Results = $dnsResults
                Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            }
            
        } catch {
            return [PSCustomObject]@{
                Target = $Target
                TestType = "DNS"
                Status = "Failed"
                Error = $_.Exception.Message
                Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            }
        }
        
    } catch {
        Write-Log "Error testing DNS resolution for $Target`: $($_.Exception.Message)" -Level "ERROR"
        return [PSCustomObject]@{
            Target = $Target
            TestType = "DNS"
            Status = "Error"
            Error = $_.Exception.Message
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
    }
}

function Export-TestResults {
    param(
        [array]$Results,
        [string]$Format,
        [string]$FilePath
    )
    
    try {
        Write-Log "Exporting test results to: $FilePath"
        
        switch ($Format) {
            "CSV" {
                $Results | Export-Csv -Path "$FilePath.csv" -NoTypeInformation -Encoding UTF8
                Write-Log "CSV results exported successfully"
            }
            "JSON" {
                $Results | ConvertTo-Json -Depth 3 | Out-File -FilePath "$FilePath.json" -Encoding UTF8
                Write-Log "JSON results exported successfully"
            }
            "HTML" {
                # TODO: Implement HTML dashboard generation
                Write-Log "HTML export not yet implemented - falling back to CSV" -Level "WARNING"
                $Results | Export-Csv -Path "$FilePath.csv" -NoTypeInformation -Encoding UTF8
            }
            default {
                Write-Log "Unknown format: $Format - using CSV" -Level "WARNING"
                $Results | Export-Csv -Path "$FilePath.csv" -NoTypeInformation -Encoding UTF8
            }
        }
        
    } catch {
        Write-Log "Error exporting test results: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# TODO: Add additional functions:
# - Test-CertificateValidation
# - Test-BandwidthMeasurement
# - Invoke-TraceRoute
# - Send-AlertEmail
# - Start-ContinuousMonitoring

### Main Execution ###
try {
    Write-Log "=== Network Connectivity Testing Started ==="
    Write-Log "Test Type: $TestType"
    Write-Log "Output Format: $OutputFormat"
    
    # Get target list
    $script:targetList = Get-TargetList
    Write-Log "Testing connectivity to $($script:targetList.Count) target(s)"
    
    # Parse port list
    $portList = $Ports -split "," | ForEach-Object { [int]$_.Trim() }
    
    # Initialize results collection
    $allResults = @()
    
    # Process each target
    foreach ($target in $script:targetList) {
        Write-Log "Processing target: $target"
        
        switch ($TestType) {
            "Ping" {
                $result = Test-PingConnectivity -Target $target -Count $Count -Timeout $Timeout
                $allResults += $result
            }
            "Port" {
                $result = Test-PortConnectivity -Target $target -PortList $portList -Timeout $Timeout
                $allResults += $result
            }
            "DNS" {
                $result = Test-DNSResolution -Target $target -Timeout $Timeout
                $allResults += $result
            }
            "All" {
                $pingResult = Test-PingConnectivity -Target $target -Count $Count -Timeout $Timeout
                $portResult = Test-PortConnectivity -Target $target -PortList $portList -Timeout $Timeout
                $dnsResult = Test-DNSResolution -Target $target -Timeout $Timeout
                $allResults += @($pingResult, $portResult, $dnsResult)
            }
        }
    }
    
    # Calculate overall statistics
    $totalTests = $allResults.Count
    $healthyTests = ($allResults | Where-Object { $_.Status -eq "Healthy" }).Count
    $warningTests = ($allResults | Where-Object { $_.Status -eq "Warning" }).Count
    $criticalTests = ($allResults | Where-Object { $_.Status -eq "Critical" }).Count
    $errorTests = ($allResults | Where-Object { $_.Status -eq "Error" }).Count
    
    $overallSuccessRate = if ($totalTests -gt 0) { [math]::Round(($healthyTests / $totalTests) * 100, 2) } else { 0 }
    
    # Output results based on format
    switch ($OutputFormat) {
        "Console" {
            Write-Host "`n=== NETWORK CONNECTIVITY TEST RESULTS ===" -ForegroundColor Cyan
            Write-Host "Overall Success Rate: $overallSuccessRate%" -ForegroundColor $(if ($overallSuccessRate -ge 80) { "Green" } elseif ($overallSuccessRate -ge 50) { "Yellow" } else { "Red" })
            Write-Host "Tests: $totalTests | Healthy: $healthyTests | Warning: $warningTests | Critical: $criticalTests | Error: $errorTests"
            
            foreach ($result in $allResults) {
                $color = switch ($result.Status) {
                    "Healthy" { "Green" }
                    "Warning" { "Yellow" }
                    "Critical" { "Red" }
                    "Error" { "Magenta" }
                    default { "White" }
                }
                Write-Host "  $($result.Target) [$($result.TestType)]: $($result.Status)" -ForegroundColor $color
            }
        }
        default {
            $reportPath = "$OutputPath\NetworkTest_$($TestType)_$(Get-Date -Format 'yyyy-MM-dd_HH-mm-ss')"
            Export-TestResults -Results $allResults -Format $OutputFormat -FilePath $reportPath
        }
    }
    
    $message = "Network connectivity testing completed. Overall success rate: $overallSuccessRate%"
    Write-Log $message
    
    # Return JSON response
    $jsonResult = New-JsonReturn -Success "true" -Status "completed" -Message $message -Data @{
        OverallSuccessRate = $overallSuccessRate
        TotalTests = $totalTests
        HealthyTests = $healthyTests
        WarningTests = $warningTests
        CriticalTests = $criticalTests
        ErrorTests = $errorTests
        Results = $allResults
    }
    
    if ($OutputFormat -eq "JSON") {
        Write-Output $jsonResult
    }
    
    Write-Log "=== Network Connectivity Testing Completed ==="
    
} catch {
    Write-Log "=== Network Connectivity Testing Failed ===" -Level "ERROR"
    Write-Log "Error: $($_.Exception.Message)" -Level "ERROR"
    
    $errorResponse = New-JsonReturn -Success "false" -Status "failed" -Message "Network testing failed: $($_.Exception.Message)"
    Write-Output $errorResponse
    
    exit 1
}
