<#
.SYNOPSIS
    Installs the [SOFTWARE_NAME] agent on Windows systems with remote installation capabilities.

.DESCRIPTION
    This script installs the [SOFTWARE_NAME] agent on Windows systems. It performs the following operations:
    1. Validates system prerequisites and compatibility
    2. Downloads or locates the agent installer
    3. Performs silent installation with specified configuration
    4. Validates the installation and service status
    
    The script supports both local and remote installations with automatic credential management.
    All configuration values are loaded from the centralized scriptConfigs.json file.
    
    For remote installations, specify the ServerName parameter. Credentials will be automatically loaded
    from XML files specified in the configuration, making the script completely self-contained.

.PARAMETER ServerName
    Optional remote server name. If provided, the script will install the agent on the remote server instead of locally.
    Credentials are automatically loaded from XML files specified in the configuration.

.PARAMETER InstallPath
    The installation path for the agent. Default value is loaded from scriptConfigs.json.

.PARAMETER AgentSourcePath
    The path to the agent installer. If not provided, the script will use the default from configuration.

.PARAMETER ServiceAccount
    The service account for the agent (optional).

.PARAMETER ServiceAccountPassword
    The password for the service account (optional).

.PARAMETER LogPath
    The path where installation logs will be written. Default value is loaded from scriptConfigs.json.

.PARAMETER Force
    Force reinstallation even if agent is already installed.

.PARAMETER OutputJson
    Return results in JSON format suitable for automation and API integration.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for service accounts.

.PARAMETER CredentialTarget
    Specifies which credential file to use. Valid values: "ppe", "dev", "admin", "cli". Default is production credentials.

.EXAMPLE
    # Basic installation using configuration defaults
    .\Install-SoftwareAgent-Template.ps1

.EXAMPLE
    # Installation with custom parameters
    .\Install-SoftwareAgent-Template.ps1 -InstallPath "C:\Program Files\CustomPath" -Force

.EXAMPLE
    # Installation with JSON output for automation
    .\Install-SoftwareAgent-Template.ps1 -OutputJson

.EXAMPLE
    # Remote installation with stored credentials
    .\Install-SoftwareAgent-Template.ps1 -ServerName "RemoteServer01" -UseStoredCredentials -CredentialTarget "admin"

.EXAMPLE
    # Remote installation using Config function
    $cred = Get-Credential
    Config[SOFTWARE_NAME] -serverName "RemoteServer01" -user $cred -Force

.NOTES
    File Name   : Install-SoftwareAgent-Template.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in centralized location
    Compatible  : Windows Server 2016, 2019, 2022
    
    Version 1.0 - Initial template for software agent installation
    Version 1.1 - Updated to use centralized config file and credential system
    Version 1.2 - Added remote installation capabilities and API-friendly JSON returns
#>

[CmdletBinding()]
param(
    [string]$ServerName,
    
    [string]$InstallPath,
    
    [string]$AgentSourcePath,
    
    [string]$ServiceAccount,
    
    [SecureString]$ServiceAccountPassword,
    
    [string]$LogPath,
    
    [switch]$Force,
    
    [switch]$OutputJson,
    
    [switch]$UseStoredCredentials,
    
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
    [ValidateSet("ppe", "dev", "admin", "cli", "")]
    [string]$CredentialTarget = ""
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Resolve-Path "$currentPath\..\..\..\Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Paths ###
$credPath = $config.GlobalConfig.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adminCredLocation = "$credPath\windowsCreds.xml"
$mudCreds = "$credPath\prdDomainCreds.xml"
$ppeCreds = "$credPath\ppeDomainCreds.xml"
$devCreds = "$credPath\devDomainCreds.xml"

### Script Variables ###
$ErrorActionPreference = "Stop"
# TODO: Update these variables based on your software configuration section
$tempPath = $config.[SOFTWARE_NAME]Config.TempPath
$agentInstaller = $config.[SOFTWARE_NAME]Config.AgentInstaller

### Software Configuration Settings - Loaded from scriptConfigs.json ###
# TODO: Update these configuration references based on your software
$defaultSourceServer = $config.[SOFTWARE_NAME]Config.SourceServer
$defaultSourcePath = $config.[SOFTWARE_NAME]Config.AgentPath

### Set default values from config if not provided ###
if (-not $InstallPath) {
    $InstallPath = $config.[SOFTWARE_NAME]Config.DefaultInstallPath
}

if (-not $LogPath) {
    $LogPath = $config.[SOFTWARE_NAME]Config.DefaultLogPath
}

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
   
    $agentInfo = @{}
    try {
        # TODO: Update this section based on your software's detection method
        $installedAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*[SOFTWARE_NAME]*" } -ErrorAction SilentlyContinue
        if ($installedAgent) {
            $agentInfo.AgentName = $installedAgent.Name
            $agentInfo.AgentVersion = $installedAgent.Version
        }
        
        # TODO: Update service name based on your software
        $service = Get-Service -Name $config.[SOFTWARE_NAME]Config.ServiceName -ErrorAction SilentlyContinue
        if ($service) {
            $agentInfo.ServiceStatus = $service.Status.ToString()
        }
    }
    catch {
        # Ignore errors during data collection
    }
    
    $objectReturn = @{
        computerName     = $env:COMPUTERNAME
        installationPath = $InstallPath
        agentInfo        = $agentInfo
        timeStamp        = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $LogPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-SoftwareCredentials {
    Write-Log "Getting software service account credentials..."
    
    if ($UseStoredCredentials) {
        try {
            $credFile = $mudCreds 
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials from file for user: $($storedCred.UserName)"
                return @{
                    Username = $storedCred.UserName
                    Password = $storedCred.Password
                }
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials from file: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    if ($ServiceAccount -and $ServiceAccountPassword) {
        Write-Log "Using provided service account credentials"
        return @{
            Username = $ServiceAccount
            Password = $ServiceAccountPassword
        }
    }
    
    Write-Log "No service account credentials provided - will use default authentication"
    return $null
}

function Test-SoftwarePrerequisites {
    Write-Log "Checking system prerequisites..."
    
    $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
    Write-Log "Operating System: $osVersion"
    
    # TODO: Update supported OS versions based on your software requirements
    $supportedVersionsPattern = "Server 2016|Server 2019|Server 2022"
    if ($osVersion -notmatch $supportedVersionsPattern) {
        Write-Log "Warning: This script is designed for Windows Server 2016, 2019, and 2022. Detected: $osVersion" -Level "WARNING"
    }
    
    # TODO: Add additional prerequisite checks specific to your software
    
    Write-Log "Prerequisites check completed successfully"
}

function Test-SoftwareInstalled {
    try {
        # TODO: Update detection logic based on your software
        $service = Get-Service -Name $config.[SOFTWARE_NAME]Config.ServiceName -ErrorAction SilentlyContinue
        $installPath = Test-Path $InstallPath
        
        if ($service -and $installPath) {
            Write-Log "Software agent is already installed. Service status: $($service.Status)" "INFO"
            return $true
        }
        
        return $false
    }
    catch {
        return $false
    }
}

# TODO: Add additional functions specific to your software installation process
# Examples:
# - Get-SoftwareAgentInstaller
# - Install-SoftwareAgent  
# - Set-SoftwareConfiguration
# - Test-SoftwareInstallation
# - Config[SOFTWARE_NAME] (for remote installations)

### Main Execution ###
try {
    Write-Log "=== [SOFTWARE_NAME] Agent Installation Started ==="
    
    if ($ServerName) {
        Write-Log "Remote installation requested for server: $ServerName"
        # TODO: Implement remote installation logic
        # Call your Config[SOFTWARE_NAME] function here
    } else {
        Write-Log "Local installation on: $env:COMPUTERNAME"
        
        Test-SoftwarePrerequisites
        
        if (Test-SoftwareInstalled -and -not $Force) {
            $message = "[SOFTWARE_NAME] agent is already installed. Use -Force to reinstall."
            Write-Log $message "WARNING"
            
            if ($OutputJson) {
                $result = New-JsonReturn -Success "true" -Status "ALREADY_INSTALLED" -Message $message
                Write-Output $result
            } else {
                Write-Host $message -ForegroundColor Yellow
            }
            exit 0
        }
        
        # TODO: Implement your installation steps here
        # 1. Get installer path
        # 2. Install software
        # 3. Configure software
        # 4. Test installation
        
        Write-Log "=== [SOFTWARE_NAME] Agent Installation Completed Successfully ==="
        
        if ($OutputJson) {
            $successResponse = New-JsonReturn -Success "true" -Status "completed" -Message "[SOFTWARE_NAME] Agent installation completed successfully"
            Write-Output $successResponse
        }
    }
}
catch {
    Write-Log "=== [SOFTWARE_NAME] Agent Installation Failed ===" -Level "ERROR"
    Write-Log "Error: $($_.Exception.Message)" -Level "ERROR"
    Write-Log "Check the installation log at: $LogPath" -Level "ERROR"
    
    if ($OutputJson) {
        $errorData = @{
            errorDetails = $_.Exception.Message
            logPath      = $LogPath
        }
        $errorResponse = New-JsonReturn -Success "false" -Status "failed" -Message "[SOFTWARE_NAME] Agent installation failed" -Data $errorData
        Write-Output $errorResponse
    }
    
    exit 1
}
