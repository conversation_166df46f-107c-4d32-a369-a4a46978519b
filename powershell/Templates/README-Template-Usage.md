# PowerShell Software Installation Template

This template provides a standardized structure for creating software installation scripts based on the patterns used in the SCOM, SCCM, and Qualys installation scripts.

## Template Features

### ✅ **Standardized Structure**
- Comprehensive synopsis with parameter documentation
- Consistent parameter definitions and validation
- Centralized configuration file integration
- Error handling and logging

### ✅ **Core Functions Included**
- `New-JsonReturn` - Standardized JSON response formatting
- `Write-Log` - Consistent logging with timestamps
- `Get-SoftwareCredentials` - Credential management with multiple sources
- `Test-SoftwarePrerequisites` - System validation
- `Test-SoftwareInstalled` - Installation detection

### ✅ **Advanced Features**
- Remote installation capabilities
- Multiple credential sources (XML files, parameters)
- JSON output for automation/API integration
- Force reinstallation option
- Comprehensive error handling

## How to Use This Template

### 1. **Copy and Rename**
```powershell
Copy-Item "Install-SoftwareAgent-Template.ps1" "Install-YourSoftware.ps1"
```

### 2. **Replace Placeholders**
Search and replace the following placeholders throughout the file:

| Placeholder | Replace With | Example |
|-------------|--------------|---------|
| `[SOFTWARE_NAME]` | Your software name | `Splunk`, `CrowdStrike`, `LAPS` |
| `[SOFTWARE_NAME]Config` | Config section name | `SplunkConfig`, `CrowdStrikeConfig` |

### 3. **Update Configuration Section**
Add your software's configuration to `powershell/Configs/scriptConfigs.json`:

```json
{
  "GlobalConfig": { ... },
  "YourSoftwareConfig": {
    "SourceServer": "srv009484",
    "AgentPath": "D:\\AutoDeploy\\YourSoftware\\installer.msi",
    "DefaultInstallPath": "C:\\Program Files\\YourSoftware",
    "DefaultLogPath": "C:\\Windows\\Temp\\YourSoftware_Install.log",
    "TempPath": "C:\\Temp\\YourSoftware",
    "AgentInstaller": "installer.msi",
    "ServiceName": "YourSoftwareService"
  }
}
```

### 4. **Customize Functions**

#### **Update Detection Logic**
```powershell
# In New-JsonReturn function
$installedAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*YourSoftware*" }

# In Test-SoftwareInstalled function  
$service = Get-Service -Name $config.YourSoftwareConfig.ServiceName -ErrorAction SilentlyContinue
```

#### **Add Software-Specific Functions**
```powershell
function Get-YourSoftwareInstaller {
    # Logic to locate/download installer
}

function Install-YourSoftware {
    # Installation logic
}

function Set-YourSoftwareConfiguration {
    # Post-installation configuration
}

function Test-YourSoftwareInstallation {
    # Validation logic
}

function ConfigYourSoftware {
    # Remote installation function
}
```

### 5. **Update Prerequisites**
Modify the `Test-SoftwarePrerequisites` function:

```powershell
function Test-SoftwarePrerequisites {
    Write-Log "Checking system prerequisites..."
    
    # Check OS version
    $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
    $supportedVersions = "Server 2016|Server 2019|Server 2022"
    
    # Add software-specific checks
    if (-not (Test-Path "C:\Required\Dependency")) {
        throw "Required dependency not found"
    }
    
    # Check available disk space
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB
    if ($freeSpace -lt 2) {
        throw "Insufficient disk space. At least 2GB required."
    }
}
```

### 6. **Implement Main Installation Logic**
Replace the TODO comments in the main execution block:

```powershell
# Replace this section:
# TODO: Implement your installation steps here

# With your actual implementation:
$installerPath = Get-YourSoftwareInstaller
Install-YourSoftware -InstallerPath $installerPath
Set-YourSoftwareConfiguration
Test-YourSoftwareInstallation
```

## Example Implementation

Here's how the template would look for a hypothetical "Splunk" agent:

### **File Name:** `Install-SplunkAgent.ps1`

### **Replacements Made:**
- `[SOFTWARE_NAME]` → `Splunk`
- `[SOFTWARE_NAME]Config` → `SplunkConfig`

### **Configuration Added:**
```json
"SplunkConfig": {
  "SourceServer": "srv009484",
  "AgentPath": "D:\\AutoDeploy\\Splunk\\splunkforwarder.msi",
  "DefaultInstallPath": "C:\\Program Files\\SplunkUniversalForwarder",
  "DefaultLogPath": "C:\\Windows\\Temp\\Splunk_Install.log",
  "ServiceName": "SplunkForwarder"
}
```

### **Custom Functions Added:**
```powershell
function Install-SplunkAgent {
    param([string]$InstallerPath)
    
    $installArgs = @(
        "/i", "`"$InstallerPath`"",
        "/qn",
        "DEPLOYMENT_SERVER=`"splunk-ds.company.com:8089`"",
        "AGREETOLICENSE=Yes"
    )
    
    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $installArgs -Wait -PassThru
    
    if ($process.ExitCode -eq 0) {
        Write-Log "Splunk agent installed successfully"
        return $true
    } else {
        throw "Installation failed with exit code: $($process.ExitCode)"
    }
}
```

## Best Practices

1. **Always test locally first** before implementing remote installation
2. **Use the Force parameter** for development/testing
3. **Implement comprehensive logging** for troubleshooting
4. **Add software-specific validation** in the test functions
5. **Follow the existing credential management pattern** for consistency
6. **Use JSON output** for automation scenarios

## Template Benefits

- **Consistency** across all software installation scripts
- **Reduced development time** with pre-built functions
- **Standardized error handling** and logging
- **Built-in remote installation** capabilities
- **API-friendly JSON responses** for automation
- **Centralized configuration** management

This template ensures all your software installation scripts follow the same high-quality patterns established in your existing SCOM, SCCM, and Qualys scripts.
