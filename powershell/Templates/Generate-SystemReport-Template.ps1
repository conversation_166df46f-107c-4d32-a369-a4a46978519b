<#
.SYNOPSIS
    Generates comprehensive system reports for Windows systems with multiple output formats.

.DESCRIPTION
    This script generates detailed system reports including:
    1. System information and specifications
    2. Hardware inventory (CPU, Memory, Storage)
    3. Software inventory (Installed programs, Windows features)
    4. Service status and configuration
    5. Network configuration and connectivity
    6. Security and compliance status
    7. Performance metrics and trends
    
    The script supports multi-server reporting, various output formats (CSV, Excel, HTML),
    and automated distribution via email. All configuration values are loaded from
    the centralized scriptConfigs.json file.

.PARAMETER ServerList
    Comma-separated list of servers to include in the report. If not specified, runs on local system.

.PARAMETER ServerListFile
    Path to a text file containing server names (one per line).

.PARAMETER ReportType
    Type of report to generate. Valid values: "Basic", "Hardware", "Software", "Security", "Performance", "Full". Default is "Basic".

.PARAMETER OutputFormat
    Output format for the report. Valid values: "CSV", "Excel", "HTML", "JSON". Default is "CSV".

.PARAMETER OutputPath
    Path where reports will be saved. Default value is loaded from scriptConfigs.json.

.PARAMETER ReportName
    Custom name for the report file. If not specified, uses timestamp-based naming.

.PARAMETER EmailRecipients
    Comma-separated list of email recipients for the report.

.PARAMETER IncludeTrends
    Include historical trend analysis (requires previous reports for comparison).

.PARAMETER ComplianceCheck
    Include compliance checks against organizational standards.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for remote connections.

.PARAMETER CredentialTarget
    Specifies which credential file to use. Valid values: "ppe", "dev", "admin", "cli".

.PARAMETER MaxConcurrentJobs
    Maximum number of concurrent remote jobs. Default is 10.

.EXAMPLE
    # Basic system report for local system
    .\Generate-SystemReport-Template.ps1

.EXAMPLE
    # Full report for multiple servers with Excel output
    .\Generate-SystemReport-Template.ps1 -ServerList "Server01,Server02,Server03" -ReportType "Full" -OutputFormat "Excel"

.EXAMPLE
    # Hardware inventory report with email distribution
    .\Generate-SystemReport-Template.ps1 -ServerListFile "C:\Scripts\servers.txt" -ReportType "Hardware" -EmailRecipients "<EMAIL>,<EMAIL>"

.EXAMPLE
    # Security compliance report with HTML dashboard
    .\Generate-SystemReport-Template.ps1 -ReportType "Security" -OutputFormat "HTML" -ComplianceCheck -OutputPath "C:\Reports\Security"

.NOTES
    File Name   : Generate-SystemReport-Template.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in centralized location
    Compatible  : Windows Server 2016, 2019, 2022
    
    Version 1.0 - Initial template for system reporting
    Version 1.1 - Added multi-server support and Excel output
    Version 1.2 - Added compliance checking and trend analysis
#>

[CmdletBinding()]
param(
    [string]$ServerList,
    
    [string]$ServerListFile,
    
    [ValidateSet("Basic", "Hardware", "Software", "Security", "Performance", "Full")]
    [string]$ReportType = "Basic",
    
    [ValidateSet("CSV", "Excel", "HTML", "JSON")]
    [string]$OutputFormat = "CSV",
    
    [string]$OutputPath,
    
    [string]$ReportName,
    
    [string]$EmailRecipients,
    
    [switch]$IncludeTrends,
    
    [switch]$ComplianceCheck,
    
    [switch]$UseStoredCredentials,
    
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
    [ValidateSet("ppe", "dev", "admin", "cli", "")]
    [string]$CredentialTarget = "",
    
    [ValidateRange(1, 50)]
    [int]$MaxConcurrentJobs = 10
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Resolve-Path "$currentPath\..\..\..\Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Paths ###
$credPath = $config.GlobalConfig.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adminCredLocation = "$credPath\windowsCreds.xml"
$mudCreds = "$credPath\prdDomainCreds.xml"
$ppeCreds = "$credPath\ppeDomainCreds.xml"
$devCreds = "$credPath\devDomainCreds.xml"

### Script Variables ###
$ErrorActionPreference = "Continue"
$dateStamp = Get-Date -Format "yyyy-MM-dd"
$timeStamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"

### Report Configuration - Loaded from scriptConfigs.json ###
# TODO: Add ReportConfig section to your scriptConfigs.json
$defaultOutputPath = if ($config.GlobalConfig.ReportPath) { $config.GlobalConfig.ReportPath } else { "C:\Reports" }
$logPath = "$defaultOutputPath\SystemReport_$dateStamp.log"

### Set default values from config if not provided ###
if (-not $OutputPath) {
    $OutputPath = $defaultOutputPath
}

if (-not $ReportName) {
    $ReportName = "SystemReport_$($ReportType)_$timeStamp"
}

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
    
    $objectReturn = @{
        reportType   = $ReportType
        outputFormat = $OutputFormat
        timestamp    = $timeStamp
        serverCount  = if ($script:serverList) { $script:serverList.Count } else { 1 }
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $logPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-ReportCredentials {
    Write-Log "Getting credentials for report generation..."
    
    if ($UseStoredCredentials) {
        try {
            $credFile = $mudCreds
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials for user: $($storedCred.UserName)"
                return $storedCred
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    return $null
}

function Get-ServerList {
    Write-Log "Building server list..."
    
    $servers = @()
    
    if ($ServerListFile -and (Test-Path $ServerListFile)) {
        $servers = Get-Content $ServerListFile | Where-Object { $_.Trim() -ne "" }
        Write-Log "Loaded $($servers.Count) servers from file: $ServerListFile"
    } elseif ($ServerList) {
        $servers = $ServerList -split "," | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
        Write-Log "Using provided server list: $($servers.Count) servers"
    } else {
        $servers = @($env:COMPUTERNAME)
        Write-Log "Using local system only"
    }
    
    return $servers
}

function Get-SystemInformation {
    param([string]$ComputerName = $env:COMPUTERNAME)
    
    try {
        Write-Log "Collecting system information for: $ComputerName"
        
        $os = Get-WmiObject -Class Win32_OperatingSystem -ComputerName $ComputerName
        $cs = Get-WmiObject -Class Win32_ComputerSystem -ComputerName $ComputerName
        $bios = Get-WmiObject -Class Win32_BIOS -ComputerName $ComputerName
        
        $systemInfo = [PSCustomObject]@{
            ComputerName = $ComputerName
            OperatingSystem = $os.Caption
            OSVersion = $os.Version
            ServicePack = $os.ServicePackMajorVersion
            Architecture = $os.OSArchitecture
            InstallDate = $os.ConvertToDateTime($os.InstallDate)
            LastBootTime = $os.ConvertToDateTime($os.LastBootUpTime)
            Manufacturer = $cs.Manufacturer
            Model = $cs.Model
            TotalPhysicalMemoryGB = [math]::Round($cs.TotalPhysicalMemory / 1GB, 2)
            ProcessorCount = $cs.NumberOfProcessors
            LogicalProcessors = $cs.NumberOfLogicalProcessors
            BIOSVersion = $bios.SMBIOSBIOSVersion
            SerialNumber = $bios.SerialNumber
            Domain = $cs.Domain
            Workgroup = $cs.Workgroup
            ReportDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        
        return $systemInfo
        
    } catch {
        Write-Log "Error collecting system information for $ComputerName`: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Get-HardwareInventory {
    param([string]$ComputerName = $env:COMPUTERNAME)
    
    if ($ReportType -notin @("Hardware", "Full")) {
        return $null
    }
    
    try {
        Write-Log "Collecting hardware inventory for: $ComputerName"
        
        # CPU Information
        $processors = Get-WmiObject -Class Win32_Processor -ComputerName $ComputerName
        $cpuInfo = foreach ($cpu in $processors) {
            [PSCustomObject]@{
                ComputerName = $ComputerName
                Type = "CPU"
                Name = $cpu.Name
                Manufacturer = $cpu.Manufacturer
                Cores = $cpu.NumberOfCores
                LogicalProcessors = $cpu.NumberOfLogicalProcessors
                MaxClockSpeed = $cpu.MaxClockSpeed
                CurrentClockSpeed = $cpu.CurrentClockSpeed
                Architecture = $cpu.Architecture
                SocketDesignation = $cpu.SocketDesignation
            }
        }
        
        # Memory Information
        $memory = Get-WmiObject -Class Win32_PhysicalMemory -ComputerName $ComputerName
        $memoryInfo = foreach ($mem in $memory) {
            [PSCustomObject]@{
                ComputerName = $ComputerName
                Type = "Memory"
                Manufacturer = $mem.Manufacturer
                PartNumber = $mem.PartNumber
                SerialNumber = $mem.SerialNumber
                CapacityGB = [math]::Round($mem.Capacity / 1GB, 2)
                Speed = $mem.Speed
                DeviceLocator = $mem.DeviceLocator
                BankLabel = $mem.BankLabel
            }
        }
        
        # Disk Information
        $disks = Get-WmiObject -Class Win32_DiskDrive -ComputerName $ComputerName
        $diskInfo = foreach ($disk in $disks) {
            [PSCustomObject]@{
                ComputerName = $ComputerName
                Type = "Disk"
                Model = $disk.Model
                Manufacturer = $disk.Manufacturer
                SerialNumber = $disk.SerialNumber
                SizeGB = [math]::Round($disk.Size / 1GB, 2)
                InterfaceType = $disk.InterfaceType
                MediaType = $disk.MediaType
                Status = $disk.Status
            }
        }
        
        return @($cpuInfo) + @($memoryInfo) + @($diskInfo)
        
    } catch {
        Write-Log "Error collecting hardware inventory for $ComputerName`: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Get-SoftwareInventory {
    param([string]$ComputerName = $env:COMPUTERNAME)
    
    if ($ReportType -notin @("Software", "Full")) {
        return $null
    }
    
    try {
        Write-Log "Collecting software inventory for: $ComputerName"
        
        # Installed Programs
        $programs = Get-WmiObject -Class Win32_Product -ComputerName $ComputerName | Where-Object { $_.Name -ne $null }
        $softwareInfo = foreach ($program in $programs) {
            [PSCustomObject]@{
                ComputerName = $ComputerName
                Name = $program.Name
                Version = $program.Version
                Vendor = $program.Vendor
                InstallDate = $program.InstallDate
                InstallLocation = $program.InstallLocation
                UninstallString = $program.UninstallString
                SizeKB = $program.Size
            }
        }
        
        return $softwareInfo
        
    } catch {
        Write-Log "Error collecting software inventory for $ComputerName`: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Export-ReportData {
    param(
        [array]$Data,
        [string]$ReportPath,
        [string]$Format
    )
    
    try {
        Write-Log "Exporting report data to: $ReportPath"
        
        switch ($Format) {
            "CSV" {
                $Data | Export-Csv -Path "$ReportPath.csv" -NoTypeInformation -Encoding UTF8
                Write-Log "CSV report exported successfully"
            }
            "JSON" {
                $Data | ConvertTo-Json -Depth 3 | Out-File -FilePath "$ReportPath.json" -Encoding UTF8
                Write-Log "JSON report exported successfully"
            }
            "Excel" {
                # TODO: Implement Excel export (requires ImportExcel module)
                Write-Log "Excel export not yet implemented - falling back to CSV" -Level "WARNING"
                $Data | Export-Csv -Path "$ReportPath.csv" -NoTypeInformation -Encoding UTF8
            }
            "HTML" {
                # TODO: Implement HTML dashboard generation
                Write-Log "HTML export not yet implemented - falling back to CSV" -Level "WARNING"
                $Data | Export-Csv -Path "$ReportPath.csv" -NoTypeInformation -Encoding UTF8
            }
            default {
                Write-Log "Unknown format: $Format - using CSV" -Level "WARNING"
                $Data | Export-Csv -Path "$ReportPath.csv" -NoTypeInformation -Encoding UTF8
            }
        }
        
    } catch {
        Write-Log "Error exporting report data: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# TODO: Add additional functions:
# - Get-SecurityStatus
# - Get-PerformanceMetrics
# - Get-ComplianceStatus
# - Send-EmailReport
# - Generate-HTMLDashboard

### Main Execution ###
try {
    Write-Log "=== System Report Generation Started ==="
    Write-Log "Report Type: $ReportType"
    Write-Log "Output Format: $OutputFormat"
    
    # Get server list
    $script:serverList = Get-ServerList
    Write-Log "Processing $($script:serverList.Count) server(s)"
    
    # Get credentials if needed
    $credentials = Get-ReportCredentials
    
    # Initialize report data collections
    $allSystemInfo = @()
    $allHardwareInfo = @()
    $allSoftwareInfo = @()
    
    # Process each server
    foreach ($server in $script:serverList) {
        Write-Log "Processing server: $server"
        
        # Collect system information
        $systemInfo = Get-SystemInformation -ComputerName $server
        if ($systemInfo) {
            $allSystemInfo += $systemInfo
        }
        
        # Collect hardware inventory
        $hardwareInfo = Get-HardwareInventory -ComputerName $server
        if ($hardwareInfo) {
            $allHardwareInfo += $hardwareInfo
        }
        
        # Collect software inventory
        $softwareInfo = Get-SoftwareInventory -ComputerName $server
        if ($softwareInfo) {
            $allSoftwareInfo += $softwareInfo
        }
    }
    
    # Export reports based on type
    $reportPath = "$OutputPath\$ReportName"
    
    if ($ReportType -in @("Basic", "Full")) {
        Export-ReportData -Data $allSystemInfo -ReportPath "$reportPath`_SystemInfo" -Format $OutputFormat
    }
    
    if ($ReportType -in @("Hardware", "Full")) {
        Export-ReportData -Data $allHardwareInfo -ReportPath "$reportPath`_Hardware" -Format $OutputFormat
    }
    
    if ($ReportType -in @("Software", "Full")) {
        Export-ReportData -Data $allSoftwareInfo -ReportPath "$reportPath`_Software" -Format $OutputFormat
    }
    
    $message = "System report generation completed successfully. Processed $($script:serverList.Count) server(s)."
    Write-Log $message
    
    # Return JSON response if requested
    $jsonResult = New-JsonReturn -Success "true" -Status "completed" -Message $message -Data @{
        ReportPath = $reportPath
        ServersProcessed = $script:serverList.Count
        ReportsGenerated = @($ReportType)
    }
    
    Write-Output $jsonResult
    Write-Log "=== System Report Generation Completed ==="
    
} catch {
    Write-Log "=== System Report Generation Failed ===" -Level "ERROR"
    Write-Log "Error: $($_.Exception.Message)" -Level "ERROR"
    
    $errorResponse = New-JsonReturn -Success "false" -Status "failed" -Message "Report generation failed: $($_.Exception.Message)"
    Write-Output $errorResponse
    
    exit 1
}
