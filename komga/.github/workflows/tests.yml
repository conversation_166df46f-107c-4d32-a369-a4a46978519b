name: Tests

on:
  pull_request:
    paths-ignore:
      - 'komga-webui/src/locales/*'
      - 'komga-tray/src/main/resources/org/gotson/komga/*'
  push:
    branches-ignore:
      - 'dependabot/**'
  workflow_dispatch:

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]
      fail-fast: false
    name: Test server - ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup Java 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          java-package: 'jdk'
          distribution: 'temurin'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build
        run: ./gradlew build :komga-tray:jar

      - name: Upload Unit Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.os }}
          path: komga/build/test-results/

      - name: Upload Unit Test Reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-reports-${{ matrix.os }}
          path: komga/build/reports/tests/

      - name: Conveyor - compute JDK module list
        if: github.event_name == 'push' && github.repository_owner == 'gotson' && contains(matrix.os, 'ubuntu')
        uses: hydraulic-software/conveyor/actions/build@v18.1
        with:
          command: -f conveyor.detect.conf -Kapp.machines=mac.aarch64 make processed-jars
          signing_key: ${{ secrets.CONVEYOR_SIGNING_KEY }}
          agree_to_license: 1

      - name: Compare JDK required modules
        id: conveyor_compare
        if: github.event_name == 'push' && github.repository_owner == 'gotson' && contains(matrix.os, 'ubuntu')
        run: diff --unified  ./komga-tray/conveyor/required-jdk-modules.txt ./output/required-jdk-modules.txt

      - name: Upload JDK required modules
        if: steps.conveyor_compare.outcome == 'failure'
        uses: actions/upload-artifact@v4
        with:
          name: conveyor-required-jdk-modules
          path: ./output/required-jdk-modules.txt

  webui:
    runs-on: ubuntu-latest
    name: Test webui builds
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'
          cache-dependency-path: komga-webui/package-lock.json
      - name: npm install
        working-directory: komga-webui
        run: npm install
      - name: npm build
        working-directory: komga-webui
        run: npm run build
        env:
          NODE_OPTIONS: "--max-old-space-size=4096"
      - name: npm test
        working-directory: komga-webui
        run: npm run test:unit
