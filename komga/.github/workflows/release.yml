name: Release

on:
  workflow_dispatch:
    inputs:
      release_notes:
        description: Release notes (use \n for newlines)
        type: string
        required: false
      bump:
        description: 'Bump type'
        required: false
        default: 'next'
        type: choice
        options:
          - 'next'
          - 'major'
          - 'minor'
          - 'patch'
          - 'current'
      github_release:
        description: 'Create Github Release'
        default: true
        type: boolean
      conveyor-copied-site:
        description: 'Conveyor copied site'
        default: true
        type: boolean
      docker_release:
        description: 'Push Docker images'
        default: true
        type: boolean
      msstore_release:
        description: 'Release to the MS Store'
        default: true
        type: boolean

jobs:
  version:
    runs-on: macos-latest
    outputs:
      version_current: ${{ steps.versions.outputs.version_current }}
      version_next: ${{ steps.versions.outputs.version_next }}
      should_release: ${{ steps.versions.outputs.should_release }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Homebrew
        id: set-up-homebrew
        uses: Homebrew/actions/setup-homebrew@master
      - name: Install svu
        run: brew install caarlos0/tap/svu
      - name: Compute next version for release
        run: |
          echo "VERSION_NEXT=`svu ${{ inputs.bump }}`" | tee -a $GITHUB_ENV
          echo "VERSION_NEXT_SUFFIX=`svu ${{ inputs.bump }}`" | tee -a $GITHUB_ENV
      - name: Set Versions
        id: versions
        run: |
          echo "version_current=`svu current`" >> $GITHUB_OUTPUT
          echo "version_next=${{ env.VERSION_NEXT_SUFFIX }}" >> $GITHUB_OUTPUT
          [[ `svu current` != ${{ env.VERSION_NEXT }} ]] && echo "should_release=true" >> $GITHUB_OUTPUT || echo

  release:
    name: Release
    runs-on: ubuntu-latest
    needs: version
    steps:
      - name: Remove unnecessary files
        run: |
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf "$AGENT_TOOLSDIRECTORY"

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Display versions
        run: |
          echo "Current version: ${{ needs.version.outputs.version_current }}, should release: ${{ needs.version.outputs.version_next }}"

      - name: Update version in gradle.properties
        if: needs.version.outputs.should_release #only redo if the version changed
        run: sed -i -e "s/version=.*/version=${{ needs.version.outputs.version_next }}/" gradle.properties

      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'
          cache-dependency-path: komga-webui/package-lock.json

      - name: Setup Java 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          java-package: 'jdk'
          distribution: 'temurin'

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build
        run: ./gradlew :komga:prepareThymeLeaf :komga:bootJar :komga-tray:jar
        env:
          NODE_OPTIONS: "--max-old-space-size=4096"

      - name: Generate OpenAPI docs
        if: needs.version.outputs.should_release #only redo if the version changed
        run: ./gradlew :komga:generateOpenApiDocs

      - name: Create release notes
        run: |
          mkdir release_notes
          echo -e "${{ inputs.release_notes }}" >> release_notes/release_notes.md
          echo "Release notes:"
          cat release_notes/release_notes.md
          echo ""

      - name: JReleaser Changelog append
        if: needs.version.outputs.should_release #only redo if the version changed
        run: ./gradlew jreleaserChangelog
        env:
          JRELEASER_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: JReleaser Changelog output
        if: always() &&  needs.version.outputs.should_release
        uses: actions/upload-artifact@v4
        with:
          name: jreleaser-changelog
          path: |
            build/jreleaser/trace.log
            build/jreleaser/output.properties

      - name: Release commit and push
        uses: EndBug/add-and-commit@v9
        if: needs.version.outputs.should_release #only redo if the version changed
        with:
          message: 'chore(release): ${{ needs.version.outputs.version_next }} [skip ci]'
          tag: '${{ needs.version.outputs.version_next }}'
          default_author: github_actions

      - name: Retrieve the Apple private key and decode it to a file
        if: inputs.github_release || inputs.conveyor-copied-site
        env:
          APPLE_PRIVATE_KEY: ${{ secrets.APPLE_PRIVATE_KEY }}
        run: |
          mkdir ./secret
          echo $APPLE_PRIVATE_KEY | base64 --decode > ./secret/apple_private_key.p8

      - name: Conveyor make copied-site
        uses: hydraulic-software/conveyor/actions/build@v18.1
        if: inputs.conveyor-copied-site
        with:
          command: --cache-limit=2.0 -f conveyor.ci.conf make copied-site -o ./output/site
          signing_key: ${{ secrets.CONVEYOR_SIGNING_KEY }}
          agree_to_license: 1
        env:
          APPLE_ISSUER_ID: ${{ secrets.APPLE_ISSUER_ID }}
          APPLE_KEY_ID: ${{ secrets.APPLE_KEY_ID }}
          AWS_S3_BUCKET: ${{ secrets.B2_BUCKET }}
          AWS_S3_ENDPOINT: 'https://s3.us-east-005.backblazeb2.com'
          AWS_ACCESS_KEY_ID: ${{ secrets.B2_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.B2_SECRET_ACCESS_KEY }}
      - name: Upload Conveyor log
        if: always() && inputs.conveyor-copied-site
        uses: actions/upload-artifact@v4
        with:
          name: conveyor-make-copied-site
          path: ~/.cache/hydraulic/conveyor/logs/log.latest.txt

      - name: JReleaser Release
        if: inputs.github_release
        run: ./gradlew jreleaserRelease
        env:
          JRELEASER_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: JReleaser Release output
        if: always() && inputs.github_release
        uses: actions/upload-artifact@v4
        with:
          name: jreleaser-release
          path: |
            build/jreleaser/trace.log
            build/jreleaser/output.properties

      # Sometimes the workflow will fail because it's out of disk space
      - name: Cleanup Conveyor output
        run: rm -fr ./output

      - name: JReleaser Publish
        if: inputs.docker_release
        run: ./gradlew jreleaserPublish
        env:
          JRELEASER_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: JReleaser Publish output
        if: always() && inputs.docker_release
        uses: actions/upload-artifact@v4
        with:
          name: jreleaser-publish
          path: |
            build/jreleaser/trace.log
            build/jreleaser/output.properties

      - name: Conveyor - publish to Microsoft Store
        uses: hydraulic-software/conveyor/actions/build@v18.1
        if: inputs.msstore_release
        with:
          command: --cache-limit=2.0 -f conveyor.msstore.ci.conf make ms-store-release -o ./output/msstore
          signing_key: ${{ secrets.CONVEYOR_SIGNING_KEY }}
          agree_to_license: 1
        env:
          CONVEYOR_MSSTORE_CLIENT_ID: ${{ secrets.CONVEYOR_MSSTORE_CLIENT_ID }}
          CONVEYOR_MSSTORE_CLIENT_SECRET: ${{ secrets.CONVEYOR_MSSTORE_CLIENT_SECRET }}
          CONVEYOR_MSSTORE_TENANT_ID: ${{ secrets.CONVEYOR_MSSTORE_TENANT_ID }}
          AWS_S3_BUCKET: ${{ secrets.B2_BUCKET }}
          AWS_S3_ENDPOINT: 'https://s3.us-east-005.backblazeb2.com'
          AWS_ACCESS_KEY_ID: ${{ secrets.B2_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.B2_SECRET_ACCESS_KEY }}
      - name: Upload Conveyor log
        if: always() && inputs.msstore_release
        uses: actions/upload-artifact@v4
        with:
          name: conveyor-ms-store-release
          path: ~/.cache/hydraulic/conveyor/logs/log.latest.txt

  dispatch:
    needs: release
    runs-on: ubuntu-latest
    steps:
      - name: Repository Dispatch
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.REPO_ACCESS_TOKEN }}
          repository: gotson/komga-website
          event-type: komga-release
