application.version: BENCHMARK

benchmark:
  result-folder: ${project.reportsDir}/benchmark
  measurement-iterations: 5
  warmup-iterations: 1
  mode: averagetime
  #  measurement-iterations: 1
  #  warmup-iterations: 0
  #  mode: singleshottime
  result-format: text
komga:
  config-dir: ${rootDir}/config-dir
  workspace: diesel
  database:
    file: \${komga.config-dir}/\${komga.workspace}.sqlite
  lucene:
    data-directory: \${komga.config-dir}/lucene/\${komga.workspace}
  tasks-db:
    file: \${komga.config-dir}/\${komga.workspace}-tasks.sqlite
spring:
  flyway:
    enabled: true
#logging:
#  level:
#    root: ERROR
#    org.jooq.tools.LoggerListener: DEBUG
